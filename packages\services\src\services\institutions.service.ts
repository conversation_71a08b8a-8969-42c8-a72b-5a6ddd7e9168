import type { InstitutionFormRequestDTO } from '@rie/api-contracts';
import { Institution } from '@rie/domain/aggregates';
import { InstitutionNotFoundError } from '@rie/domain/errors';
import type { CollectionViewType, InstitutionTranslationFields, ResourceViewType } from '@rie/domain/types';
import {
  InstitutionTranslation,
  InstitutionTranslationCollection,
} from '@rie/domain/value-objects';
import { InstitutionsRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import * as Schema from 'effect/Schema';
import { InstitutionFormToDomainInput } from '../mappers';
import {
  InstitutionEditClientToDomainInput,
  serializeInstitutionToDetail,
  serializeInstitutionToEditClient,
  serializeInstitutionToFormEdit,
  serializeInstitutionToListItem,
  serializeInstitutionToSelectOption,
} from '../serializers';

// Type helper for institution domain input to avoid circular references
type InstitutionDomainInput = {
  guidId: string | null;
  typeId: string;
  translations: Array<{ locale: string; name: string | null }>;
};

export class InstitutionsServiceLive extends Effect.Service<InstitutionsServiceLive>()(
  'InstitutionsServiceLive',
  {
    dependencies: [InstitutionsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllInstitutions = (params: {
        locale: string;
        fallbackLocale: string;
        view: CollectionViewType;
      }) =>
        Effect.gen(function* () {
          const tStart = performance.now();
          const repo = yield* InstitutionsRepositoryLive;
          const institutions = yield* repo.findAllInstitutions();
          const afterFetch = performance.now();
          console.log(
            `[Service][Institutions] fetched ${institutions.length} institutions in ${afterFetch - tStart} ms (view=${params.view}, locale=${params.locale}, fallback=${params.fallbackLocale})`,
          );

          // Serialize based on view type
          switch (params.view) {
            case 'list': {
              const result = yield* Effect.all(
                institutions.map((institution) =>
                  serializeInstitutionToListItem(
                    institution,
                    params.locale,
                    params.fallbackLocale,
                  ),
                ),
              );
              console.log(
                `[Service][Institutions] serialized list ${result.length} items in ${performance.now() - afterFetch} ms`,
              );
              return result;
            }
            case 'select': {
              const result = yield* Effect.all(
                institutions.map((institution) =>
                  serializeInstitutionToSelectOption(
                    institution,
                    params.locale,
                    params.fallbackLocale,
                  ),
                ),
              );
              console.log(
                `[Service][Institutions] serialized select ${result.length} options in ${performance.now() - afterFetch} ms`,
              );
              return result;
            }
            case 'grid':
              return institutions;
            default: {
              const _exhaustive: never = params.view;
              throw new Error(`Unsupported view type: ${_exhaustive}`);
            }
          }
        });

      const getInstitutionById = (params: {
        id: string;
        locale: string;
        fallbackLocale: string;
        view: ResourceViewType;
      }) =>
        Effect.gen(function* () {
          const repo = yield* InstitutionsRepositoryLive;
          const institution = yield* repo.findInstitutionById(params.id);
          if (!institution) {
            return yield* Effect.fail(
              new InstitutionNotFoundError({ id: params.id }),
            );
          }

          // Serialize based on view type
          switch (params.view) {
            case 'detail': {
              return yield* serializeInstitutionToDetail(
                institution,
                params.locale,
                params.fallbackLocale,
              );
            }
            case 'edit': {
              return yield* serializeInstitutionToEditClient(institution, null);
            }
            default: {
              // TypeScript exhaustiveness check
              const _exhaustive: never = params.view;
              throw new Error(`Unsupported view type: ${_exhaustive}`);
            }
          }
        });

      const createInstitution = (params: {
        institutionDto: InstitutionFormRequestDTO;
        userId: string;
      }) =>
        Effect.gen(function* () {
          // 1. Map the incoming DTO to a clean domain input shape
          const domainInput = yield* Schema.decode(InstitutionFormToDomainInput)(
            params.institutionDto,
          );

          // 2. For new institution creation, create translation VOs from input data
          // We create minimal translation objects that will get proper IDs during save
          const translationVOs = yield* Effect.all(
            domainInput.translations.map((t) => {
              // Create a translation object with minimal required fields
              const translationData = {
                id: '', // Empty string, will be filled by DB
                dataId: '', // Empty string, will be filled by repository
                locale: t.locale,
                name: t.name,
                description: t.description,
                otherNames: t.otherNames,
                acronyms: t.acronyms,
              };
              return InstitutionTranslation.create(translationData);
            }),
          );

          const translationCollection =
            yield* InstitutionTranslationCollection.create(translationVOs);

          // 3. Create the Institution aggregate
          const institution = yield* Institution.create({
            ...domainInput,
            modifiedBy: params.userId,
            translations: translationCollection,
          });

          // 4. Save the new aggregate using the unified save method
          const repo = yield* InstitutionsRepositoryLive;
          const savedInstitution = yield* repo.save(institution);

          // 5. Serialize the result for the API response
          const rawSavedInstitution = yield* savedInstitution.toRaw();
          return yield* serializeInstitutionToFormEdit(rawSavedInstitution);
        });

      const updateInstitution = ({
        id,
        institutionDto,
        userId,
      }: {
        id: string;
        institutionDto: InstitutionFormRequestDTO | {
          id?: string;
          pseudonym: string;
          name: Array<{ locale: string; value: string }>;
          typeId: string | null;
        };
        userId: string;
      }) =>
        Effect.gen(function* () {
          const repo = yield* InstitutionsRepositoryLive;
          console.log('[Service][Institutions][Update] Incoming payload received');

          // 1. Fetch the existing institution
          const existingInstitution = yield* repo.findInstitutionById(id);
          if (!existingInstitution) {
            return yield* Effect.fail(new InstitutionNotFoundError({ id }));
          }

          // 2. Map the DTO via serializer (supports both API contract and client edit payload)
          const domainInputRaw =
            'pseudonym' in institutionDto && !('guidId' in institutionDto)
              ? (yield* Schema.decode(InstitutionEditClientToDomainInput)(
                institutionDto as {
                  id?: string;
                  pseudonym: string;
                  name: Array<{ locale: string; value: string }>;
                  typeId: string | null;
                },
              ))
              : (yield* Schema.decode(InstitutionFormToDomainInput)(
                institutionDto as InstitutionFormRequestDTO,
              ));

          // Normaliser guidId pour éviter undefined et convertir translations en mutable
          const domainInput: InstitutionDomainInput = {
            guidId: domainInputRaw.guidId ?? null,
            typeId: domainInputRaw.typeId,
            translations: [...domainInputRaw.translations] as Array<{ locale: string; name: string | null }>,
          };
          console.log('[Service][Institutions][Update] domainInput mapped');

          // 3. Merge existing translations with new translations
          const existingInstitutionData = yield* existingInstitution.toRaw();
          const existingTranslations = existingInstitutionData.translations;

          // Create a map of existing translations by locale for efficient lookup
          const existingTranslationsMap = new Map(
            existingTranslations.map((t: InstitutionTranslationFields) => [t.locale, t])
          );

          // Start with a copy of existing translations
          const mergedTranslations = new Map(existingTranslationsMap);

          // Update or add new translations
          for (const newTranslation of domainInput.translations) {
            if (existingTranslationsMap.has(newTranslation.locale)) {
              // Update existing translation, preserving id and dataId
              const existing = existingTranslationsMap.get(newTranslation.locale);
              if (existing) {
                mergedTranslations.set(newTranslation.locale, {
                  ...existing,
                  name: newTranslation.name,
                });
              }
            } else {
              // Add new translation - note: id will be generated by the database
              // so we only include it in the domain object for type compatibility
              mergedTranslations.set(newTranslation.locale, {
                id: '', // Temporary id, will be generated by DB
                locale: newTranslation.locale,
                name: newTranslation.name,
                description: null,
                otherNames: null,
                acronyms: null,
                dataId: existingInstitutionData.id,
              });
            }
          }

          // Convert to array and create translation VOs
          const allTranslationsData = Array.from(mergedTranslations.values());
          const translationVOs = yield* Effect.all(
            allTranslationsData.map((t: InstitutionTranslationFields) => InstitutionTranslation.create(t)),
          );
          const translationCollection =
            yield* InstitutionTranslationCollection.create(translationVOs);

          // 6. Call the update method on the existing aggregate
          const updatedDomainInstitution = yield* existingInstitution.update({
            guidId: domainInput.guidId ?? null,
            typeId: domainInput.typeId,
            isActive: true,
            modifiedBy: userId,
            translations: translationCollection,
          });

          // 4. Save the updated aggregate using the unified save method
          const savedInstitution = yield* repo.save(updatedDomainInstitution);
          console.log('[Service][Institutions][Update] Saved institution id:', (yield* savedInstitution.toRaw()).id);

          // 5. Serialize the result for the API response
          const rawSavedInstitution = yield* savedInstitution.toRaw();
          return yield* serializeInstitutionToFormEdit(rawSavedInstitution);
        });

      const deleteInstitution = (id: string) =>
        Effect.gen(function* () {
          const repo = yield* InstitutionsRepositoryLive;
          const existingInstitution = yield* repo.findInstitutionById(id);
          if (!existingInstitution) {
            return yield* Effect.fail(new InstitutionNotFoundError({ id }));
          }
          const result = yield* repo.deleteInstitution(id);
          return result.length > 0;
        });

      return {
        getAllInstitutions,
        getInstitutionById,
        createInstitution,
        updateInstitution,
        deleteInstitution,
      } as const;
    }),
  },
) { }