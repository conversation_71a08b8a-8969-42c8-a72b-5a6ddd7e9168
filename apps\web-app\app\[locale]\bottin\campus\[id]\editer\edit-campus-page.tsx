'use client';
import { CampusForm } from '@/app/[locale]/bottin/campus/form/campus-form';
import { LoadingResource } from '@/components/loading-resource/loading-resource';
import { useUpdateCampus } from '@/hooks/directory/campuses.hook';
import { useGetDirectoryById } from '@/hooks/directory/directory-list.hook';
import type { CampusFormSchema } from '@/schemas/bottin/campus-form-schema';
import type { CampusFormSectionKey } from '@/types/building';
import type { SupportedLocale } from '@/types/locale';

type EditCampusPageParams = {
  formSections: Record<CampusFormSectionKey, string>;
  id: string;
  locale: SupportedLocale;
};
export default function EditionCampusPage({
  formSections,
  id,
}: EditCampusPageParams) {
  const {
    data: campus,
    error,
    isPending,
  } = useGetDirectoryById<'campus', 'edit'>({
    directoryListKey: 'campus',
    id,
    view: 'edit',
  });

  if (isPending) {
    return <LoadingResource />;
  }

  if (error) {
    return <div className="p-4 text-red-500">Erreur : {error.message}</div>;
  }

  const { mutate, status } = useUpdateCampus();
  const onSubmit = (data: CampusFormSchema) => {
    mutate({ id, payload: data });
  };

  return (
    <CampusForm
      defaultValues={campus}
      formSections={formSections}
      onSubmit={onSubmit}
      status={status}
    />
  );
}
