import { PUBLIC_VISIBILITY_ID } from '@rie/constants';
import { infrastructures, infrastructuresI18N } from '@rie/db-schema/schemas';
import type { InfrastructureInput } from '@rie/domain/types';
import { PgDatabaseLive } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import * as R from 'remeda';
import {
  EQUIPMENT_RELATION_MINIMAL,
  INFRASTRUCTURE_COLUMNS,
  INFRASTRUCTURE_RELATIONS_FULL,
  INFRASTRUCTURE_RELATIONS_MINIMAL,
  INFRASTRUCTURE_SELECT_OBJECT,
} from './infrastructures.constants';

export class InfrastructuresRepositoryLive extends Effect.Service<InfrastructuresRepositoryLive>()(
  'InfrastructuresRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const dbClient = yield* PgDatabaseLive;

      /**
       * Find infrastructures with their related equipments by unit ID
       * This optimizes the access tree building by reducing multiple queries to a single query
       */
      const findInfrastructuresWithRelatedEntitiesByUnitId = dbClient.makeQuery(
        (execute, unitId: string) => {
          return execute((client) =>
            client.query.infrastructures.findMany({
              where: eq(infrastructures.unitId, unitId),
              columns: {
                id: true,
              },
              with: {
                equipments: EQUIPMENT_RELATION_MINIMAL,
              },
            }),
          );
        },
      );
      const findAllInfrastructures = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.infrastructures.findMany({
            columns: INFRASTRUCTURE_COLUMNS,
            with: INFRASTRUCTURE_RELATIONS_MINIMAL,
          }),
        );
      });

      /**
       * Find all infrastructures with public visibility
       * Optimized for unauthenticated access - filters at database level
       */
      const findAllPublicInfrastructures = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.infrastructures.findMany({
            where: eq(infrastructures.visibilityId, PUBLIC_VISIBILITY_ID),
            columns: INFRASTRUCTURE_COLUMNS,
            with: {
              ...INFRASTRUCTURE_RELATIONS_MINIMAL,
              visibility: {
                columns: {
                  id: true,
                },
                with: {
                  translations: {
                    columns: {
                      locale: true,
                      name: true,
                      description: true,
                    },
                  },
                },
              },
            },
          }),
        );
      });

      const findInfrastructureById = dbClient.makeQuery(
        (execute, id: string) => {
          return execute((client) =>
            client.query.infrastructures.findFirst({
              where: eq(infrastructures.id, id),
              columns: INFRASTRUCTURE_COLUMNS,
              with: INFRASTRUCTURE_RELATIONS_FULL,
            }),
          ).pipe(
            Effect.tapError((error) =>
              Effect.logError(
                `Database error fetching infrastructure ${id}:`,
                error,
              ),
            ),
          );
        },
      );

      const findInfrastructureByIdWithRelatedEquipments = dbClient.makeQuery(
        (execute, id: string) => {
          return execute((client) =>
            client.query.infrastructures.findFirst({
              where: eq(infrastructures.id, id),
              columns: INFRASTRUCTURE_COLUMNS,
              with: {
                ...INFRASTRUCTURE_RELATIONS_FULL,
                equipments: EQUIPMENT_RELATION_MINIMAL,
              },
            }),
          );
        },
      );

      const createInfrastructure = ({
        infrastructure,
      }: {
        infrastructure: InfrastructureInput;
      }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // Create the infrastructure
            const [createdInfrastructure] = yield* tx((client) =>
              client
                .insert(infrastructures)
                .values(R.omit(infrastructure, ['translations', 'guidId']))
                .returning(INFRASTRUCTURE_SELECT_OBJECT),
            );

            if (!createdInfrastructure) {
              return yield* Effect.fail(
                new Error('Failed to create infrastructure'),
              );
            }

            // Create the translations
            const translationsToInsert = infrastructure.translations.map(
              (translation) => ({
                dataId: createdInfrastructure.id,
                ...translation,
              }),
            );
            const createdTranslations = yield* tx((client) =>
              client
                .insert(infrastructuresI18N)
                .values(translationsToInsert)
                .returning({
                  id: infrastructuresI18N.id,
                  locale: infrastructuresI18N.locale,
                  name: infrastructuresI18N.name,
                  description: infrastructuresI18N.description,
                  otherNames: infrastructuresI18N.otherNames,
                  acronyms: infrastructuresI18N.acronyms,
                }),
            );

            // Return the infrastructure with its translations directly
            return {
              ...createdInfrastructure,
              translations: createdTranslations,
            };
          });
        });
      };

      const updateInfrastructure = ({
        infrastructureId,
        infrastructure,
      }: {
        infrastructureId: string;
        infrastructure: InfrastructureInput;
      }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // Update the infrastructure
            const [updatedInfrastructure] = yield* tx((client) =>
              client
                .update(infrastructures)
                .set(R.omit(infrastructure, ['translations', 'guidId']))
                .where(eq(infrastructures.id, infrastructureId))
                .returning(INFRASTRUCTURE_SELECT_OBJECT),
            );

            // Delete existing translations
            yield* tx((client) =>
              client
                .delete(infrastructuresI18N)
                .where(eq(infrastructuresI18N.dataId, infrastructureId)),
            );

            // Insert new translations
            const translationsToInsert = infrastructure.translations.map(
              (translation) => ({
                dataId: infrastructureId,
                ...translation,
              }),
            );

            const updatedTranslations = yield* tx((client) =>
              client
                .insert(infrastructuresI18N)
                .values(translationsToInsert)
                .returning({
                  id: infrastructuresI18N.id,
                  locale: infrastructuresI18N.locale,
                  name: infrastructuresI18N.name,
                  description: infrastructuresI18N.description,
                  otherNames: infrastructuresI18N.otherNames,
                  acronyms: infrastructuresI18N.acronyms,
                }),
            );

            // Return the infrastructure with its translations directly
            return {
              ...updatedInfrastructure,
              translations: updatedTranslations,
            };
          });
        });
      };

      const deleteInfrastructure = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client
            .delete(infrastructures)
            .where(eq(infrastructures.id, id))
            .returning({ id: infrastructures.id }),
        );
      });

      return {
        findInfrastructuresWithRelatedEntitiesByUnitId,
        findAllInfrastructures,
        findAllPublicInfrastructures,
        findInfrastructureById,
        findInfrastructureByIdWithRelatedEquipments,
        createInfrastructure,
        updateInfrastructure,
        deleteInfrastructure,
      } as const;
    }),
  },
) {}
