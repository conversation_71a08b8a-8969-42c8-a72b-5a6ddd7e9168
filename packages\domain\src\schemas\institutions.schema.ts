import {
  DbInstitutionI18NSelectSchema,
  DbInstitutionInputSchema,
  DbInstitutionSelectSchema,
  DbInstitutionTypeI18NSelectSchema,
  DbInstitutionTypeSelectSchema,
} from '@rie/db-schema/entity-schemas';
import type { DbInstitutionI18N } from '@rie/db-schema/entity-types';
import * as Schema from 'effect/Schema';
import {
  optionalFieldWth1500MaxLengthSchema,
  optionalFieldWth150MaxLengthSchema,
} from './base.schema';
import { LocaleSchema } from './query.schema';
import { createDbTranslationSchema } from './translation.schema';

// — Full Institution shape
export const InstitutionSchema = Schema.Struct({
  ...DbInstitutionSelectSchema.omit('modifiedBy').fields,
  modifiedBy: Schema.NullishOr(Schema.String),
  translations: Schema.Array(
    DbInstitutionI18NSelectSchema.omit('id', 'dataId'),
  ),
  type: Schema.NullishOr(
    Schema.Struct({
      ...DbInstitutionTypeSelectSchema.fields,
      translations: Schema.Array(
        DbInstitutionTypeI18NSelectSchema.omit('id', 'dataId'),
      ),
    }),
  ),
});

// — Translation input schema
export const InstitutionI18NInputSchema = Schema.Struct({
  locale: LocaleSchema,
  name: optionalFieldWth150MaxLengthSchema('Name'),
  description: optionalFieldWth1500MaxLengthSchema('Description'),
  otherNames: optionalFieldWth150MaxLengthSchema('Other Names'),
  acronyms: optionalFieldWth150MaxLengthSchema('Acronyms'),
});

// — Institution List view schema (for directory table)
export const InstitutionListSchema = Schema.Struct({
  id: Schema.String,
  text: Schema.String, // nom par défaut depuis traductions
  acronym: Schema.NullishOr(Schema.String), // acronyme depuis traductions
  establishmentType: Schema.NullishOr(Schema.String), // type d'établissement
  lastUpdatedAt: Schema.String,
  createdAt: Schema.String,
  uid: Schema.NullishOr(Schema.String),
  // Champs internes pour les actions
  guidId: Schema.NullishOr(Schema.String),
  typeId: Schema.NullishOr(Schema.String),
});

// — Institution Select view schema
export const InstitutionSelectSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

// — Institution Edit view schema
export const InstitutionEditSchema = Schema.Struct({
  id: Schema.String,
  guidId: Schema.NullishOr(Schema.String),
  typeId: Schema.String,
  translations: Schema.Array(InstitutionI18NInputSchema),
});

// — Institution Detail view schema (same as list for now)
export const InstitutionDetailSchema = InstitutionListSchema;

// API Input Schema - for external API validation (excludes dataId)
export const DbInstitutionI18NInputSchema = createDbTranslationSchema<DbInstitutionI18N>({
  fields: {
    name: {
      required: false,
      maxLength: 150,
    },
    description: {
      required: false,
      maxLength: 1500,
    },
    otherNames: {
      required: false,
      maxLength: 150,
    },
    acronyms: {
      required: false,
      maxLength: 150,
    },
  },
});

// Domain Creation Schema - for domain object creation (includes dataId)
export const DbInstitutionI18NCreationSchema = Schema.extend(
  DbInstitutionI18NInputSchema,
  Schema.Struct({
    dataId: Schema.String,
  }),
);

// Full Data Schema - for complete database records (includes id and dataId)
export const DbInstitutionI18NDataSchema = Schema.extend(
  DbInstitutionI18NCreationSchema,
  Schema.Struct({
    id: Schema.String,
  }),
);

export const DbInstitutionDataSchema = Schema.Struct({
  ...DbInstitutionSelectSchema.fields,
  translations: Schema.Array(DbInstitutionI18NDataSchema),
});

// — Input (create/update) shape
export const InstitutionInputSchema = Schema.Struct({
  ...DbInstitutionInputSchema.omit('id').fields,
  translations: Schema.Array(DbInstitutionI18NInputSchema),
});

// — Database schema (for serializers)
export const DbInstitutionSchema = InstitutionSchema;
