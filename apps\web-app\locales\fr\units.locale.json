{"table": {"columns": {"name": "Nom", "acronym": "Acronyme", "parent": "Parent", "createdAt": "<PERSON><PERSON><PERSON>", "lastUpdatedAt": "Dernière mise à jour", "actions": "Actions"}}, "form": {"sections": {"description": {"title": "Description", "generalInfo": {"title": "Informations générales", "fields": {"unitType": {"label": "Type d'unité", "options": {"Administrative": "Unité administrative", "Research": "Unité de recherche"}}, "acronym": {"label": "Acronyme", "error": {"max": "Acronyme ne doit pas contenir plus de {max} caractères"}}, "name": {"label": "Nom en {locale}", "error": {"required": "Nom est requis", "max": "Nom ne peut pas contenir plus de {max} caractères"}}, "alias": {"label": "<PERSON>as en {locale}", "tooltip": "Autres ou anciens noms", "error": {"max": "Alias ne doit pas contenir plus de {max} caractères"}}, "parentUnit": {"label": "Unité parent", "error": {"required": "Unité parent est requise"}}, "relatedOrganizations": {"label": "Organisations associées"}}}}, "affiliations": {"title": "Affiliations", "fields": {}}}}}