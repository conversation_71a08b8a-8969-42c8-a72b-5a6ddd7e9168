import * as fs from 'node:fs/promises';
import { INPUT_PATH, OUTPUT_PATH } from '../constants';
import { logConverterRecordCounts } from '../converter-validator';
import type {
  Mapping,
  MySqlI18NBaseReturn,
  PostgresI18NBaseReturn,
} from '../types';
import { BaseConverter } from './base-converter';

export class ExcellenceHubMigrationConverter extends BaseConverter {
  private excellenceHubMappings: Mapping[] = [];

  private parseInsertStatement(
    sqlStatement: string,
    sqlContent: string,
  ): MySqlI18NBaseReturn[] {
    const values = this.extractValuesFromInsertStatement(
      sqlStatement,
      sqlContent,
      'pole_excellence',
    );

    return [
      {
        id: Number.parseInt(values.id),
      },
    ];
  }

  private convertToPostgres(
    mysqlExcellenceHub: MySqlI18NBaseReturn,
  ): PostgresI18NBaseReturn {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.excellenceHubMappings.push({
      mysqlId: mysqlExcellenceHub.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
    };
  }

  async convertFile(): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(INPUT_PATH, 'utf8');

      // Extract INSERT statements for pole_excellence table (old MySQL table name)
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'pole_excellence',
      );

      if (insertStatements.length === 0) {
        console.log('No pole_excellence INSERT statements found.');
        return;
      }

      const allPostgresExcellenceHubs: PostgresI18NBaseReturn[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlExcellenceHubs = this.parseInsertStatement(
          statement,
          sqlContent,
        );
        const postgresExcellenceHubs = mysqlExcellenceHubs.map((eh) =>
          this.convertToPostgres(eh),
        );
        allPostgresExcellenceHubs.push(...postgresExcellenceHubs);
      }

      // Generate output with both inserts and mappings
      const postgresInsertsWithMappings =
        this.generatePostgresBaseTableInsertWithMappings(
          allPostgresExcellenceHubs,
          'excellence_hubs',
          'Excellence Hub Inserts',
        );

      // Create the output directory if it doesn't exist
      const outputDir = OUTPUT_PATH.substring(0, OUTPUT_PATH.lastIndexOf('/'));
      await this.safeMkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await this.safeAppendFile(OUTPUT_PATH, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.excellenceHubMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'pole_excellence', postgres: 'excellence_hubs' },
        idMappings,
      );

      console.log('Conversion completed successfully!');

      // Validate the conversion - this will log any record count differences
      await logConverterRecordCounts('ExcellenceHubMigrationConverter', [
        { mysql: 'pole_excellence', postgres: 'excellence_hubs' },
      ]);
      console.log(
        `- Found ${allPostgresExcellenceHubs.length} excellence_hubs records`,
      );
    } catch (error) {
      console.error('Error converting file:', error);
      throw error;
    }
  }
}
