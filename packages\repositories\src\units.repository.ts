import { DBSchema } from '@rie/db-schema';
import { institutionAssociatedUnits } from '@rie/db-schema/schemas';
import { Unit } from '@rie/domain/aggregates';
import { UnitPersistenceError } from '@rie/domain/errors';
import type { UnitData } from '@rie/domain/types';
import { PgDatabaseLive } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';

export class UnitsRepositoryLive extends Effect.Service<UnitsRepositoryLive>()(
  'UnitsRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const dbClient = yield* PgDatabaseLive;

      const findAllUnits = () => {
        const getAllUnitsEffect = dbClient.makeQuery((execute) =>
          execute((client) =>
            client.query.units.findMany({
              columns: {
                id: true,
                guidId: true,
                typeId: true,
                isActive: true,
                parentId: true,
                createdAt: true,
                updatedAt: true,
                modifiedBy: true,
              },
              with: {
                translations: {
                  columns: {
                    id: true,
                    dataId: true,
                    locale: true,
                    name: true,
                    description: true,
                    otherNames: true,
                    acronyms: true,
                  },
                },
                parent: {
                  columns: {
                    id: true,
                    type: true,
                    institutionId: true,
                    unitId: true,
                  },
                  with: {
                    institution: {
                      columns: {
                        id: true,
                      },
                      with: {
                        translations: {
                          columns: {
                            locale: true,
                            name: true,
                          },
                        },
                      },
                    },
                    unit: {
                      columns: {
                        id: true,
                      },
                      with: {
                        translations: {
                          columns: {
                            locale: true,
                            name: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            }),
          ),
        );

        const rawUnitsEffect = getAllUnitsEffect();
        return pipe(
          rawUnitsEffect,
          Effect.flatMap((rawUnits) =>
            // Use Effect.all to hydrate every raw unit in the array in parallel
            Effect.all(
              rawUnits.map((rawUnit) =>
                Unit.fromDatabaseData(rawUnit as UnitData),
              ),
            ),
          ),
        );
      };

      const findUnitById = (id: string) => {
        // 1. Create the Effect that fetches the raw data by calling the function returned from makeQuery.
        const getRawUnitEffect = dbClient.makeQuery((execute, id: string) =>
          execute((client) =>
            client.query.units.findFirst({
              where: eq(DBSchema.units.id, id),
              columns: {
                id: true,
                guidId: true,
                typeId: true,
                isActive: true,
                parentId: true,
                createdAt: true,
                updatedAt: true,
                modifiedBy: true,
              },
              with: {
                translations: {
                  columns: {
                    id: true,
                    dataId: true,
                    locale: true,
                    name: true,
                    description: true,
                    otherNames: true,
                    acronyms: true,
                  },
                },
                type: {
                  columns: {
                    id: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
              },
            }),
          ),
        );

        const rawUnitEffect = getRawUnitEffect(id);

        // 2. Now that we have a valid Effect, we pipe it into our hydration logic.
        return pipe(
          rawUnitEffect,
          Effect.flatMap((rawUnit) => {
            if (!rawUnit) {
              return Effect.succeed(null);
            }
            return Unit.fromDatabaseData(rawUnit);
          }),
        );
      };

      const save = (unit: Unit) =>
        pipe(
          unit.toRaw(), // Dehydrate the aggregate to a plain data object, this also validates its state.
          Effect.flatMap((unitData) =>
            dbClient.transaction((tx) =>
              Effect.gen(function* () {
                // Use Drizzle's insert...onConflictDoUpdate to handle both cases atomically.
                const [savedUnit] = yield* tx((client) =>
                  client
                    .insert(DBSchema.units)
                    .values(unitData)
                    .onConflictDoUpdate({
                      target: DBSchema.units.id,
                      set: {
                        isActive: unitData.isActive,
                        guidId: unitData.guidId,
                        typeId: unitData.typeId,
                        parentId: unitData.parentId,
                        modifiedBy: unitData.modifiedBy,
                      },
                    })
                    .returning({ id: DBSchema.units.id }),
                );

                if (!savedUnit) {
                  return yield* Effect.fail(
                    new UnitPersistenceError({
                      reason: 'Failed to save unit to database',
                    }),
                  );
                }

                // Replace all translations for this unit.
                yield* tx((client) =>
                  client
                    .delete(DBSchema.unitsI18N)
                    .where(eq(DBSchema.unitsI18N.dataId, savedUnit.id)),
                );

                if (unitData.translations.length > 0) {
                  yield* tx((client) =>
                    client.insert(DBSchema.unitsI18N).values(
                      unitData.translations.map((t) => ({
                        ...t,
                        dataId: unitData.id,
                      })),
                    ),
                  );
                }

                // Re-fetch the unit to get the final state with DB-generated timestamps
                // and ensure we return a fully consistent aggregate.
                const finalUnit = yield* findUnitById(savedUnit.id);
                if (!finalUnit) {
                  return yield* Effect.fail(
                    new UnitPersistenceError({
                      unitId: savedUnit.id,
                      reason: 'Failed to re-fetch unit after save operation',
                    }),
                  );
                }
                return finalUnit;
              }),
            ),
          ),
        );

      // — Delete a unit
      const deleteUnit = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client
            .delete(DBSchema.units)
            .where(eq(DBSchema.units.id, id))
            .returning({ id: DBSchema.units.id }),
        );
      });

      /**
       * Find all related entities (units, infrastructures, equipments) for an institution
       * This optimizes the access tree building by using a single query with joins
       */
      const findUnitsWithRelatedEntitiesByInstitutionId = dbClient.makeQuery(
        (execute, institutionId: string) => {
          return execute((client) =>
            client.query.institutionAssociatedUnits.findMany({
              where: eq(
                institutionAssociatedUnits.institutionId,
                institutionId,
              ),
              with: {
                unit: {
                  columns: {
                    id: true,
                  },
                  with: {
                    infrastructures: {
                      columns: {
                        id: true,
                      },
                      with: {
                        equipments: {
                          columns: {
                            id: true,
                          },
                        },
                      },
                    },
                  },
                },
              },
            }),
          );
        },
      );

      return {
        findUnitsWithRelatedEntitiesByInstitutionId,
        findAllUnits,
        findUnitById,
        deleteUnit,
        save,
      } as const;
    }),
  },
) { }
