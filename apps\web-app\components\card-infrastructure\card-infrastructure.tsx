import { Button, buttonVariants } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { StatusBadge } from '@/components/ui/status-badge';
import { resourceStatus } from '@/constants/infrastructures';
import { Link } from '@/lib/navigation';
import { cn } from '@/lib/utils';
import type { Infrastructure } from '@/types/infrastructure';
import { useTranslations } from 'next-intl';

import { PermissionGate } from '../permissions/permission-gate';

type InfrastructureCardProps = {
  className?: string;
} & Pick<
  Infrastructure,
  | 'equipmentCount'
  | 'id'
  | 'jurisdiction'
  | 'name'
  | 'parentUnit'
  | 'status'
  | 'statusText'
  | 'type'
>;

export const InfrastructureCard = ({
  className,
  equipmentCount,
  id,
  jurisdiction,
  name,
  parentUnit,
  status,
  statusText,
  type,
}: InfrastructureCardProps) => {
  const t = useTranslations('common');

  return (
    <Card className={cn('flex h-full flex-col', className)}>
      <div className="flex-grow">
        <CardHeader>
          <CardDescription>{type}</CardDescription>
          <CardTitle className="flex shrink-0 grow basis-auto">
            {name}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <StatusBadge status={resourceStatus[status]}>
              {statusText}
            </StatusBadge>
          </div>
          <div className="mb-4 text-sm">
            {jurisdiction} - {parentUnit}
          </div>
          <div className="text-sm font-bold">
            {t('equipmentCount', { count: equipmentCount })}
          </div>
        </CardContent>
      </div>
      <CardFooter className="mt-auto flex justify-end gap-x-4">
        <PermissionGate
          resourceId={id}
          action="update"
          resourceType="infrastructure"
        >
          <Button asChild>
            <Link
              className={cn(buttonVariants({ variant: 'default' }), 'sm')}
              href={{
                params: { id },
                pathname: '/infrastructures/[id]/editer',
              }}
            >
              {t('edit')}
            </Link>
          </Button>
        </PermissionGate>
        <Button asChild>
          <Link
            className={cn(buttonVariants({ variant: 'outline' }), 'sm')}
            href={{ params: { id }, pathname: '/infrastructures/[id]' }}
          >
            {t('show')}
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
};
