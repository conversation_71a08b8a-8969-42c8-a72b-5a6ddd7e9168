import type { controlledList } from '@/constants/controlled-list';
import type {
  CampusAddressDTO,
  CivicAddressDTO,
} from '@/schemas/common-schema';
import type { BuildingFull, EstablishmentFull } from '@/types/building';
import type { PersonFull } from '@/types/controlled-list';
import type { IdType } from '@/types/directory/project';
import type { FacetValueFull } from '@/types/filters';
import type { Territory } from '@/types/infrastructure';
import type { SupportedLocale } from '@/types/locale';
import type { CollectionViewParamType } from '@rie/domain/types';
import type { directoryList } from './directory/directory';

export type CollectionOptionsParams<
  View extends CollectionViewParamType['view'],
> = {
  locale: SupportedLocale;
  view: View;
};


export type ComboboxItem = {
  id: string;
  text: string;
};

export type BasePageParams = {
  params: Promise<{
    locale: SupportedLocale;
  }>;
};

export type SearchPageParams = {
  params: Promise<
    BasePageParams['params'] extends Promise<infer T> ? T : never
  >;
  searchParams?: Record<string, string> | string | string[][] | URLSearchParams;
};

export type ResourcePageParams = BasePageParams & {
  params: Promise<{ id: string }>;
};

export type CamelCase<S extends string> = S extends `${infer P1}-${infer P2}`
  ? `${Lowercase<P1>}${Capitalize<P2>}`
  : Lowercase<S>;

export type MapperReturnType<TData, TFacet> = {
  data: TData;
  facets: Record<string, TFacet>;
} & Omit<ApiReturnType<TData>, 'data' | 'facets'>;

export type ApiReturnType<TData> = {
  count: number;
} & Omit<ApiResponse<TData>, 'info'>;

export type ApiSingleReturnType<TData> = Omit<
  ApiResponse<TData>,
  'facets' | 'info'
>;

export type ApiInfiniteReturnType<TData> = {
  count: number;
  offset: number;
} & Omit<ApiResponse<TData>, 'info'>;

//return type for controlled list
export type ControlledListReturnType<TData> = {
  count: number;
  data: TData;
  facets: Record<string, FacetValueFull[]>;
  offset: number;
};

export type ApiResponse<TData> = {
  data: TData;
  facets: Record<string, FacetValueFull[]>;
  info: ApiResponseInfo;
};
export type ApiResponseInfo = {
  limit: number;
  start: number;
  total_pages: number;
  total_records: number;
};

export type PageDetailsParams = {
  params: Promise<
    IdType & (BasePageParams['params'] extends Promise<infer T> ? T : never)
  >;
};

export type SelectOption = {
  label: string;
  value: string;
};

export const isSelectOption = (option: unknown): option is SelectOption => {
  return (
    typeof option === 'object' &&
    option !== null &&
    'label' in option &&
    'value' in option &&
    typeof option.label === 'string' &&
    typeof option.value === 'string' &&
    option.label !== null &&
    option.value !== null
  );
};

export type Email = {
  address: string;
  id: number;
  text: string;
};

export const exhaustiveMatchingGuard = (value: never): never => {
  throw new Error(`${value} isn't handled`);
};

export type EntityLocaleDictionary = {
  en?: null | string;
  fr?: null | string;
};

export type InfrastructureType = {
  descriptions?: EntityLocaleDictionary;
  id: number;
  nom: string;
  noms: EntityLocaleDictionary;
  slug: string;
  text: string;
  uid: string;
};

export type Manager = {
  name_with_lastname_first: string;
  type_contenu: string;
} & PersonFull;

export type LocationBuildingFull = {
  batiment: BuildingFull;
  id: number;
  juridiction: EstablishmentFull;
  numero: string;
  pseudonyme: null | string;
  slug: null | string;
  text: string;
};

export type EntityLocation = {
  id: number;
  batiment: BuildingFull | null;
  estLocal: boolean;
  local: LocationBuildingFull | null;
  territory: null | Territory;
  text: string;
};

export type EntityBase = {
  descriptions: EntityLocaleDictionary;
  id: number;
  nom: string;
  noms: EntityLocaleDictionary;
  slug: string;
  text: string;
  uid: string;
};

export type ControlledListKey = keyof typeof controlledList;
export type DirectoryListKey = keyof typeof directoryList;

export type EntityType = EntityBase;
export type Visibility = EntityBase;

export const isRootError = (
  error: unknown,
): error is { root: { message: string } } => {
  return (
    typeof error === 'object' &&
    error !== null &&
    'root' in error &&
    typeof error.root === 'object' &&
    error.root !== null &&
    'message' in error.root &&
    typeof error.root.message === 'string'
  );
};

export const isFieldError = (error: unknown): error is { message: string } => {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof error.message === 'string'
  );
};

export const isNestedFieldError = (
  error: unknown,
): error is { message: string } => {
  if (!error || typeof error !== 'object') {
    return false;
  }

  return (
    ('message' in error && typeof error.message === 'string') ||
    Object.values(error).some(
      (value) => value && typeof value === 'object' && 'message' in value,
    )
  );
};

export type ViewMode = 'cards' | 'table';
export type ViewModeWithEmpty = '' & ViewMode;

type CivicAddressDetails = {
  city: string;
  country: string;
  country_code: string;
  county: string;
  house_number: string;
  'ISO3166-2-lvl4': string;
  neighbourhood: string;
  postcode: string;
  region: string;
  road: string;
  state: string;
  suburb: string;
};

export type CivicAddressFull = {
  address: CivicAddressDetails;
  addresstype: string;
  boundingbox: string[];
  category: string;
  display_name: string;
  importance: number;
  lat: string;
  licence: string;
  lon: string;
  name: string;
  osm_id: number;
  osm_type: string;
  place_id: number;
  place_rank: number;
  type: string;
};

export type CampusAddressMap = {
  data: CampusAddressDTO;
  addressType: 'campus';
};

export type CivicAddressMap = {
  data: CivicAddressDTO;
  addressType: 'civicAddress';
};

export type AddressMap = {
  campus: CampusAddressMap;
  civicAddress: CivicAddressMap;
};
export type AddressType = keyof AddressMap;

export type CampusAddressPayload = {
  batiment: IdType | null;
  estLocal: true;
  local: IdType | null;
  territory: null;
};

export type CivicAddressPayload = {
  batiment: null;
  local: null;
  estLocal: false;
  territory: Territory;
};
export type AddressPayload = CampusAddressPayload | CivicAddressPayload;

export type RequestStatus = 'idle' | 'pending' | 'error' | 'success';
