import { FieldWithTranslations } from '@/app/[locale]/equipements/form/components/field-with-translations';
import { getFieldErrorMessage } from '@/app/[locale]/equipements/form/helpers';
import { DatePickerField } from '@/components/form-fields/datepicker-field';
import { FormSubsection } from '@/components/form-subsection/form-subsection';
import { Input } from '@/components/ui/input';
import { useAvailableLocale } from '@/hooks/useAvailableLocale';
import { useTranslatedField } from '@/hooks/useTranslatedField';
import type { VendorFormSchema } from '@/schemas/bottin/vendor-form-schema';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

export const GeneralInfo = () => {
  const availableLocale = useAvailableLocale();
  const tManufacturers = useTranslations(
    'directory.form.sections.description.generalInfo',
  );
  const tManufacturerBase = useTranslations('directory.form.sections');
  const tCommon = useTranslations('common');
  const { control, formState } = useFormContext<VendorFormSchema>();

  const nameFields = useTranslatedField(control, 'name');

  const nameError = getFieldErrorMessage(formState.errors, 'name');
  const nameErrorMessage = nameError ? tManufacturerBase(nameError) : undefined;

  const aliasFields = useTranslatedField(control, 'alias');
  const aliasError = getFieldErrorMessage(formState.errors, 'alias');
  const aliasErrorMessage = aliasError
    ? tManufacturerBase(aliasError)
    : undefined;

  return (
    <FormSubsection title={tManufacturers('title')}>
      <FieldWithTranslations
        control={control}
        errorMessage={nameErrorMessage}
        fieldComponent={(field) => <Input {...field} />}
        fieldName="name"
        fields={nameFields.fields}
        label={(locale) =>
          tManufacturers('fields.name.label', { locale: tCommon(locale) })
        }
        maxLength={150}
        onAddTranslation={nameFields.handleAddTranslation}
        onRemoveTranslation={nameFields.handleRemoveTranslation}
        required={true}
      />
      <FieldWithTranslations
        control={control}
        errorMessage={aliasErrorMessage}
        fieldComponent={(field) => <Input {...field} />}
        fieldName="alias"
        fields={aliasFields.fields}
        label={(locale) =>
          tManufacturers('fields.alias.label', { locale: tCommon(locale) })
        }
        maxLength={1000}
        onAddTranslation={aliasFields.handleAddTranslation}
        onRemoveTranslation={aliasFields.handleRemoveTranslation}
        tooltip={(locale) =>
          tManufacturers('fields.alias.tooltip', {
            locale: tCommon(locale ?? availableLocale),
          })
        }
      />
      <DatePickerField
        fieldLabel={tManufacturers('fields.dateEnd.label')}
        fieldName="dateEnd"
      />
    </FormSubsection>
  );
};
