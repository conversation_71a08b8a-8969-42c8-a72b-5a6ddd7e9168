import * as Data from 'effect/Data';

export class FundingProjectNotFoundError extends Data.TaggedError(
  'FundingProjectNotFoundError',
)<{ readonly id: string }> { }

export class FundingProjectValidationError extends Data.TaggedError(
  'FundingProjectValidationError',
)<{
  readonly message: string;
  readonly fields?: Record<string, string>;
}> { }

export class FundingProjectInvariantViolationError extends Data.TaggedError(
  'FundingProjectInvariantViolationError',
)<{
  readonly fundingProjectId: string;
  readonly reason: string;
}> { }
