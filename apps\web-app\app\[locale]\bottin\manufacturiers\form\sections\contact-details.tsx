import { PhoneField } from '@/app/[locale]/bottin/manufacturiers/form/sections/phone-field';
import { CivicCampusAddress } from '@/components/civic-campus-address/civic-campus-address';
import { FormSubsection } from '@/components/form-subsection/form-subsection';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CAMPUS_ADDRESS_DEFAULT_VALUE } from '@/constants/common';
import { manufacturerFormSections } from '@/constants/directory/manufacturer';
import { useAvailableLocale } from '@/hooks/useAvailableLocale';
import type { VendorFormSchema } from '@/schemas/bottin/vendor-form-schema';
import { useTranslations } from 'next-intl';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { FiPlus, FiTrash2 } from 'react-icons/fi';

export const ContactDetails = () => {
  const t = useTranslations(
    'directory.form.sections.description.contactDetails',
  );
  const locale = useAvailableLocale();

  const tCommon = useTranslations('common');

  const { control } = useFormContext<VendorFormSchema>();

  const {
    append: appendPhone,
    fields: phoneFields,
    remove: removePhone,
  } = useFieldArray<VendorFormSchema>({
    control,
    name: 'phones',
  });

  const {
    append: appendContactType,
    fields: contactTypeFields,
    remove: removeContactType,
  } = useFieldArray<VendorFormSchema>({
    control,
    name: 'contacts',
  });

  return (
    <Card id={manufacturerFormSections.contactDetails.key}>
      <CardHeader>
        <CardTitle className="text-3xl">{t('title')}</CardTitle>
      </CardHeader>
      <CardContent>
        <FormSubsection title={t('fields.phone.label')}>
          <div className="flex flex-col gap-y-4">
            <div className="flex flex-col gap-y-3">
              {phoneFields.map((field, index) => (
                <div className="flex flex-col" key={field.id}>
                  <PhoneField index={index} />
                  <Button
                    className="self-end"
                    onClick={() => removePhone(index)}
                    type="button"
                    variant="destructive"
                  >
                    <FiTrash2 className="mr-2 h-4 w-4" /> {tCommon('delete')}
                  </Button>
                </div>
              ))}
            </div>
            <Button
              className="self-end"
              onClick={() =>
                appendPhone({
                  description: [
                    {
                      locale,
                      value: '',
                    },
                  ],
                  phone: '',
                })
              }
              type="button"
            >
              <FiPlus className="mr-2 h-4 w-4" /> {t('addContact')}
            </Button>
          </div>
        </FormSubsection>
        <FormSubsection title={tCommon('components.address.title')}>
          <div className="flex flex-col gap-y-4">
            <div className="flex flex-col gap-y-3">
              {contactTypeFields.map((field, index) => (
                <CivicCampusAddress
                  key={field.id}
                  addressFieldPath={`contacts.${index}`}
                  totalAddresses={contactTypeFields.length}
                  onRemoveAddress={() => removeContactType(index)}
                  withAddressTypeToggle
                  required={false}
                />
              ))}
            </div>
            <Button
              className="self-end"
              onClick={() => appendContactType(CAMPUS_ADDRESS_DEFAULT_VALUE)}
              type="button"
            >
              <FiPlus className="mr-2 h-4 w-4" /> {t('addAddress')}
            </Button>
          </div>
        </FormSubsection>
      </CardContent>
    </Card>
  );
};
