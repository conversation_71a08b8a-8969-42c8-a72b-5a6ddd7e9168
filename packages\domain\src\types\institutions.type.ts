import type {
  DbInstitutionDataSchema,
  DbInstitutionI18NDataSchema,
  DbInstitutionI18NInputSchema,
  InstitutionDetailSchema,
  InstitutionEditSchema,
  InstitutionInputSchema,
  InstitutionListSchema,
  InstitutionSchema,
  InstitutionSelectSchema,
} from '../schemas/institutions.schema';

import type * as Schema from 'effect/Schema';

// Institution type based on database entity
export type Institution = Schema.Schema.Type<typeof InstitutionSchema>;

// InstitutionInput type for API operations
export type InstitutionInput = Schema.Schema.Type<
  typeof InstitutionInputSchema
>;

export type InstitutionList = Schema.Schema.Type<typeof InstitutionListSchema>;
export type InstitutionSelect = Schema.Schema.Type<
  typeof InstitutionSelectSchema
>;
export type InstitutionEdit = Schema.Schema.Type<typeof InstitutionEditSchema>;
export type InstitutionDetail = Schema.Schema.Type<
  typeof InstitutionDetailSchema
>;
export type InstitutionData = Schema.Schema.Type<typeof DbInstitutionDataSchema>;
export type InstitutionTranslationFields = Schema.Schema.Type<
  typeof DbInstitutionI18NDataSchema
>;
export type InstitutionI18NInput = Schema.Schema.Type<
  typeof DbInstitutionI18NInputSchema
>;
