import * as fs from 'node:fs/promises';
import { INPUT_PATH, OUTPUT_PATH } from '../constants';
import { logConverterRecordCounts } from '../converter-validator';
import type {
  Mapping,
  MySqlI18NBaseReturn,
  PostgresI18NBaseReturn,
} from '../types';
import { BaseConverter } from './base-converter';

export class UnitTypeMigrationConverter extends BaseConverter {
  private unitTypeMappings: Mapping[] = [];

  private parseInsertStatement(
    sqlStatement: string,
    sqlContent: string,
  ): MySqlI18NBaseReturn[] {
    const values = this.extractValuesFromInsertStatement(
      sqlStatement,
      sqlContent,
      'type_unite',
    );

    return [
      {
        id: Number.parseInt(values.id),
      },
    ];
  }

  private convertToPostgres(
    mysqlUnitType: MySqlI18NBaseReturn,
  ): PostgresI18NBaseReturn {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.unitTypeMappings.push({
      mysqlId: mysqlUnitType.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
    };
  }

  async convertFile(): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(INPUT_PATH, 'utf8');

      // Extract INSERT statements for type_unite table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'type_unite',
      );

      if (insertStatements.length === 0) {
        console.log('No type_unite INSERT statements found.');
        return;
      }

      const allPostgresUnitTypes: PostgresI18NBaseReturn[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlUnitTypes = this.parseInsertStatement(statement, sqlContent);
        const postgresUnitTypes = mysqlUnitTypes.map((ut) =>
          this.convertToPostgres(ut),
        );
        allPostgresUnitTypes.push(...postgresUnitTypes);
      }

      // Generate output with both inserts and mappings
      const postgresInsertsWithMappings =
        this.generatePostgresBaseTableInsertWithMappings(
          allPostgresUnitTypes,
          'unit_types',
          'Unit Type Inserts',
        );

      // Create the output directory if it doesn't exist
      const outputDir = OUTPUT_PATH.substring(0, OUTPUT_PATH.lastIndexOf('/'));
      await this.safeMkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await this.safeAppendFile(OUTPUT_PATH, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.unitTypeMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'type_unite', postgres: 'unit_types' },
        idMappings,
      );

      console.log('Conversion completed successfully!');

      // Validate the conversion - this will log any record count differences
      await logConverterRecordCounts('UnitTypeMigrationConverter', [
        { mysql: 'type_unite', postgres: 'unit_types' },
      ]);
      console.log(`- Found ${allPostgresUnitTypes.length} unit_types records`);
      console.log(`- PostgreSQL inserts written to: ${OUTPUT_PATH}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
