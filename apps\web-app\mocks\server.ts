import { infrastructuresHandlers } from '@/mocks/handlers/infrastructures';
import { peopleHandlers } from '@/mocks/handlers/people';
import { setupServer } from 'msw/node';
import { authHandlers } from './handlers/auth.handler';
import { functionalitiesHandlers } from './handlers/functionality.handler';

export const server = setupServer(
  ...authHandlers,
  ...infrastructuresHandlers,
  ...peopleHandlers,
  ...functionalitiesHandlers,
);
