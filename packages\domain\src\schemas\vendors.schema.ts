import {
  Db<PERSON>endorI18NSelectSchema,
  DbVendorInputSchema,
  DbVendorSelectSchema,
} from '@rie/db-schema/entity-schemas';
import type { DbVendorI18N } from '@rie/db-schema/entity-types';
import * as Schema from 'effect/Schema';
import {
  optionalFieldWth1500MaxLengthSchema,
  optionalFieldWth150MaxLengthSchema,
  requiredFieldWth150MaxLengthSchema,
} from './base.schema';
import { LocaleSchema } from './query.schema';
import { createDbTranslationSchema } from './translation.schema';

// — Full Vendor shape (with translations)
export const VendorSchema = Schema.Struct({
  ...DbVendorSelectSchema.fields,
  // modifiedBy: Schema.NullishOr(Schema.String),
  translations: Schema.Array(DbVendorI18NSelectSchema),
});

// — Translation Input for API
export const VendorI18NInputSchema = Schema.Struct({
  locale: LocaleSchema,
  name: requiredFieldWth150MaxLengthSchema('Name'),
  website: optionalFieldWth150MaxLengthSchema('Website'),
  description: optionalFieldWth1500MaxLengthSchema('Description'),
  otherNames: optionalFieldWth150MaxLengthSchema('Other Names'),
});

// — Vendor List view schema (for directory table)
export const VendorListSchema = Schema.Struct({
  id: Schema.String,
  name: Schema.String, // nom par défaut depuis traductions
  dateEnd: Schema.NullishOr(Schema.String), // date de fin
  lastUpdatedAt: Schema.String,
  // Champs internes pour les actions
  startDate: Schema.NullishOr(Schema.String),
  endDate: Schema.NullishOr(Schema.String),
});

// — Vendor Select view schema
export const VendorSelectSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

// — Vendor Edit view schema
export const VendorEditSchema = Schema.Struct({
  id: Schema.String,
  startDate: Schema.NullishOr(Schema.String),
  endDate: Schema.NullishOr(Schema.String),
  translations: Schema.Array(VendorI18NInputSchema),
  phones: Schema.optional(
    Schema.Array(
      Schema.Struct({
        description: Schema.Array(
          Schema.Struct({ locale: Schema.String, value: Schema.String }),
        ),
        phone: Schema.NullishOr(Schema.String),
      }),
    ),
  ),
  website: optionalFieldWth150MaxLengthSchema('Website'),
  local: Schema.optional(Schema.String),
  building: Schema.optional(Schema.String),
  civicAddresses: Schema.optional(Schema.Array(Schema.Unknown)),
  contacts: Schema.Array(
    Schema.Union(
      Schema.Struct({
        addressType: Schema.Literal('campus'),
        data: Schema.Struct({
          listedBuilding: Schema.Struct({
            label: Schema.NullishOr(Schema.String),
            value: Schema.NullishOr(Schema.String),
          }),
          listedLocal: Schema.Struct({
            label: Schema.NullishOr(Schema.String),
            value: Schema.NullishOr(Schema.String),
          }),
        }),
      }),
      Schema.Struct({
        addressType: Schema.Literal('civicAddress'),
        data: Schema.Struct({
          city: Schema.String,
          countryCode: Schema.String,
          fullAddress: Schema.String,
          lat: Schema.String,
          lon: Schema.String,
          placeId: Schema.String,
          postalCode: Schema.String,
          state: Schema.String,
          streetName: Schema.String,
          streetNumber: Schema.String,
        }),
      }),
    ),
  ),
});

// — Vendor Detail view schema (same as list for now)
export const VendorDetailSchema = VendorListSchema;

// — Database schema (for serializers)
export const DbVendorSchema = VendorSchema;

// — Extended DB Vendor with relations for serializers
export const DbVendorWithRelationsSchema = Schema.Struct({
  id: Schema.String,
  isActive: Schema.NullishOr(Schema.Boolean),
  startDate: Schema.NullishOr(Schema.String),
  endDate: Schema.NullishOr(Schema.String),
  createdAt: Schema.String,
  updatedAt: Schema.String,
  modifiedBy: Schema.NullishOr(Schema.String),
  translations: Schema.Array(
    Schema.Struct({
      dataId: Schema.String,
      id: Schema.String,
      locale: Schema.String,
      name: Schema.NullishOr(Schema.String),
      website: Schema.NullishOr(Schema.String),
      description: Schema.NullishOr(Schema.String),
      otherNames: Schema.NullishOr(Schema.String),
    })
  ),
  contacts: Schema.optional(Schema.Array(
    Schema.Struct({
      vendorId: Schema.String,
      vendorContactId: Schema.String,
      contact: Schema.Struct({
        id: Schema.String,
        phoneNumber: Schema.NullishOr(Schema.String),
        email: Schema.NullishOr(Schema.String),
        addressId: Schema.NullishOr(Schema.String),
        createdAt: Schema.String,
        updatedAt: Schema.String,
        modifiedBy: Schema.NullishOr(Schema.String),
        translations: Schema.Array(
          Schema.Struct({
            id: Schema.String,
            dataId: Schema.String,
            locale: Schema.String,
            description: Schema.NullishOr(Schema.String),
          })
        ),
        address: Schema.optional(Schema.Struct({
          id: Schema.String,
          addressType: Schema.Union(Schema.Literal('campus'), Schema.Literal('civic')),
          campusAddressId: Schema.NullishOr(Schema.String),
          civicAddressId: Schema.NullishOr(Schema.String),
          campusAddress: Schema.optional(Schema.Struct({
            id: Schema.String,
            room_id: Schema.String,
          })),
          civicAddress: Schema.optional(Schema.Struct({
            id: Schema.String,
            street1: Schema.String,
            street2: Schema.NullishOr(Schema.String),
            city: Schema.String,
            state: Schema.String,
            postalCode: Schema.String,
            countryCode: Schema.String,
            placeId: Schema.String,
            lat: Schema.NullishOr(Schema.String),
            lon: Schema.NullishOr(Schema.String),
          })),
        })),
      }),
    })
  )),
});

// — Vendor Form Input Schema (for serializers - represents form data structure)
export const VendorFormInputSchema = Schema.Struct({
  id: Schema.optional(Schema.String),
  name: Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      value: Schema.String,
    }),
  ),
  alias: Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      value: Schema.String,
    }),
  ),
  dateEnd: Schema.NullishOr(Schema.String),
  contacts: Schema.Array(
    Schema.Union(
      Schema.Struct({
        addressType: Schema.Literal('campus'),
        data: Schema.Struct({
          listedBuilding: Schema.Struct({
            label: Schema.NullishOr(Schema.String),
            value: Schema.NullishOr(Schema.String),
          }),
          listedLocal: Schema.Struct({
            label: Schema.NullishOr(Schema.String),
            value: Schema.NullishOr(Schema.String),
          }),
        }),
      }),
      Schema.Struct({
        addressType: Schema.Literal('civicAddress'),
        data: Schema.Struct({
          city: Schema.String,
          countryCode: Schema.String,
          fullAddress: Schema.String,
          lat: Schema.String,
          lon: Schema.String,
          placeId: Schema.String,
          postalCode: Schema.String,
          state: Schema.String,
          streetName: Schema.String,
          streetNumber: Schema.String,
        }),
      }),
    ),
  ),
  phones: Schema.Array(
    Schema.Struct({
      description: Schema.Array(
        Schema.Struct({ locale: Schema.String, value: Schema.String }),
      ),
      phone: Schema.optional(Schema.String),
    }),
  ),
});

// API Input Schema - for external API validation (excludes dataId)
export const DbVendorI18NInputSchema = createDbTranslationSchema<DbVendorI18N>({
  fields: {
    name: {
      required: true,
      maxLength: 150,
    },
    website: {
      required: false,
      maxLength: 150,
    },
    description: {
      required: false,
      maxLength: 1500,
    },
    otherNames: {
      required: false,
      maxLength: 150,
    },
  },
});

// — Input (create/update) shape
export const VendorInputSchema = Schema.Struct({
  ...DbVendorInputSchema.omit('id').fields,
  translations: Schema.Array(DbVendorI18NInputSchema),
});

// Domain Creation Schema - for domain object creation (includes dataId)
export const DbVendorI18NCreationSchema = Schema.extend(
  DbVendorI18NInputSchema,
  Schema.Struct({
    dataId: Schema.String,
  }),
);

// Full Data Schema - for complete database records (includes id and dataId)
export const DbVendorI18NDataSchema = Schema.extend(
  DbVendorI18NCreationSchema,
  Schema.Struct({
    id: Schema.String,
  }),
);

export const DbVendorDataSchema = Schema.Struct({
  ...DbVendorSelectSchema.fields,
  translations: Schema.Array(DbVendorI18NDataSchema),
});
