import type { CampusFull } from '@/types/building';

export const campusMock: CampusFull[] = [
  {
    id: '1',
    uid: 'mtl',
    etablissement: {
      uid: 'G000001',
      id: 'G000001',
      text: 'Université de Montréal',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
    },
    noms: {
      fr: 'Campus principal',
    },
    nom: 'Campus principal',
    text: 'Campus principal',
  },
  {
    id: '2',
    uid: 'hip',
    etablissement: {
      uid: 'G000001',
      id: 'G000001',
      text: 'Université de Montréal',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
    },
    noms: {
      fr: 'Campus des Laurentides',
    },
    nom: 'Campus des Laurentides',
    text: 'Campus des Laurentides',
  },
  {
    id: '3',
    uid: 'meg',
    etablissement: {
      uid: 'G000001',
      id: 'G000001',
      text: 'Université de Montréal',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
    },
    noms: {
      fr: 'Observatoire du Mont-Mégantic',
    },
    nom: 'Observatoire du Mont-Mégantic',
    text: 'Observatoire du Mont-Mégantic',
  },
  {
    id: '4',
    uid: 'hya',
    etablissement: {
      uid: 'G000001',
      id: 'G000001',
      text: 'Université de Montréal',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
    },
    noms: {
      fr: 'Campus de St-Hyacinthe',
    },
    nom: 'Campus de St-Hyacinthe',
    text: 'Campus de St-Hyacinthe',
  },
  {
    id: '5',
    uid: 'mil',
    etablissement: {
      uid: 'G000001',
      id: 'G000001',
      text: 'Université de Montréal',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
    },
    noms: {
      fr: 'Campus MIL',
    },
    nom: 'Campus MIL',
    text: 'Campus MIL',
  },
  {
    id: '6',
    uid: 'lav',
    etablissement: {
      uid: 'G000001',
      id: 'G000001',
      text: 'Université de Montréal',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
    },
    noms: {
      fr: 'Campus de Laval',
    },
    nom: 'Campus de Laval',
    text: 'Campus de Laval',
  },
  {
    id: '7',
    uid: 'bot',
    etablissement: {
      uid: 'G000001',
      id: 'G000001',
      text: 'Université de Montréal',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
    },
    noms: {
      fr: 'Institut de recherche en biologie végétale',
    },
    nom: 'Institut de recherche en biologie végétale',
    text: 'Institut de recherche en biologie végétale',
  },
];
