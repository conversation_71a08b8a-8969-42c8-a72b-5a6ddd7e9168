'use client';
import { FinancingProjectForm } from '@/app/[locale]/bottin/projets-financement/form/financing-project-form';
import { LoadingResource } from '@/components/loading-resource/loading-resource';
import { useGetDirectoryById } from '@/hooks/directory/directory-list.hook';
import { useUpdateFundingProject } from '@/hooks/directory/funding-projects.hook';
import type { FinancingProjectFormSchema } from '@/schemas/bottin/financing-project-form-schema';
import type { ProjectFormSectionKey } from '@/types/directory/project';
import type { SupportedLocale } from '@/types/locale';

type EditFundingProjectPageParams = {
  formSections: Record<ProjectFormSectionKey, string>;
  id: string;
  locale: SupportedLocale;
};
export default function EditionFundingProjectPage({
  formSections,
  id,
}: EditFundingProjectPageParams) {
  const {
    data: fundingProject,
    error,
    isPending,
  } = useGetDirectoryById<'fundingProjects', 'edit'>({
    directoryListKey: 'fundingProjects',
    id,
    view: 'edit',
  });

  if (isPending) {
    return <LoadingResource />;
  }

  if (error) {
    return <div className="p-4 text-red-500">Erreur : {error.message}</div>;
  }

  const { mutate, status } = useUpdateFundingProject();
  const onSubmit = (data: FinancingProjectFormSchema) => {
    mutate({ id, payload: data });
  };

  return (
    <FinancingProjectForm
      defaultValues={fundingProject}
      formSections={formSections}
      onSubmit={onSubmit}
      status={status}
    />
  );
}
