import { InfrastructureCard } from '@/components/card-infrastructure/card-infrastructure';
import { ResourceGrid } from '@/components/resource-grid/resource-grid';
import { ScrollToTopButton } from '@/components/scroll-to-top-button/scroll-to-top-button';
import type { Infrastructure } from '@/types/infrastructure';

export const InfrastructureGrid = ({
  className,
  infrastructures,
}: {
  className?: string;
  infrastructures: Infrastructure[];
}) => {
  return (
    <div className="relative">
      <ResourceGrid className={className}>
        {infrastructures.map((infrastructure: Infrastructure) => (
          <InfrastructureCard
            className="h-full"
            key={infrastructure.id}
            {...infrastructure}
          />
        ))}
      </ResourceGrid>
      <ScrollToTopButton />
    </div>
  );
};
