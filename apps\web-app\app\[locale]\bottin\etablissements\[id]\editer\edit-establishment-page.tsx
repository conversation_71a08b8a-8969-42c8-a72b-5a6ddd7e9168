'use client';
import { EstablishmentForm } from '@/app/[locale]/bottin/etablissements/form/establishment-form';
import { LoadingResource } from '@/components/loading-resource/loading-resource';
import { useGetDirectoryById } from '@/hooks/directory/directory-list.hook';
import { useUpdateEstablishment } from '@/hooks/directory/establishments.hook';
import type { EstablishmentFormSchema } from '@/schemas/bottin/establishment-form-schema';
import type { EstablishmentFormSectionKey } from '@/types/directory/establishement';
import type { SupportedLocale } from '@/types/locale';

type EditEstablishmentPageParams = {
  formSections: Record<EstablishmentFormSectionKey, string>;
  id: string;
  locale: SupportedLocale;
};
export default function EditionEstablishmentPage({
  formSections,
  id,
}: EditEstablishmentPageParams) {
  const {
    data: establishment,
    error,
    isPending,
  } = useGetDirectoryById<'establishment', 'edit'>({
    directoryListKey: 'establishment',
    id,
    view: 'edit',
  });

  if (isPending) {
    return <LoadingResource />;
  }

  if (error) {
    return <div className="p-4 text-red-500"> Erreur : {error.message}</div>;
  }

  const { mutate, status } = useUpdateEstablishment();
  const onSubmit = (data: EstablishmentFormSchema) => {
    mutate({ id, payload: data });
  };

  return (
    <EstablishmentForm
      defaultValues={establishment}
      formSections={formSections}
      onSubmit={onSubmit}
      status={status}
    />
  );
}
