import EditionManufacturerPage from '@/app/[locale]/bottin/manufacturiers/[id]/editer/edit-manufacturer-page';
import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { manufacturerFormSections } from '@/constants/directory/manufacturer';
import { getQueryClientOptions } from '@/constants/query-client';
import { getDirectoryByIdOptions, getDirectoryListOptions } from '@/hooks/directory/directory-list.options';
import { redirect } from '@/lib/navigation';
import type { DirectoryListKey, PageDetailsParams } from '@/types/common';
import { auth } from '@rie/auth';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';

type EditManufacturerPageParams = PageDetailsParams;
export default async function EditManufacturerPage(
  props: EditManufacturerPageParams,
) {
  const params = await props.params;

  const { id, locale } = params;
  const sessionData = await auth.api.getSession({ headers: await headers() });

  if (!sessionData?.user) {
    return redirect({
      href: {
        pathname: '/login',
        query: { from: `/bottin/manufacturiers/${id}/editer` },
      },
      locale,
    });
  }

  const formSections = await getFormSections({
    resourceName: 'manufacturers',
    sections: manufacturerFormSections,
  });

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const t0 = performance.now();

  const directoryLists: DirectoryListKey[] = ['local', 'building'];

  const manufacturer = await Promise.all([
    queryClient.fetchQuery(
      getDirectoryByIdOptions<'supplier', 'edit'>({
        directoryListKey: 'supplier',
        id,
        view: 'edit',
      }),
    ),
    ...directoryLists.map((directoryListKey) =>
      queryClient.prefetchQuery(
        getDirectoryListOptions({
          directoryListKey,
          locale,
          view: 'select',
        }),
      ),
    ),
  ]);

  const t1 = performance.now();

  console.log(
    `Call to fetch controlledLists "local", "building" and "manufacturer" took ${t1 - t0} milliseconds.`,
  );

  if (!manufacturer) {
    return notFound();
  }

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <EditionManufacturerPage
        formSections={formSections}
        id={id}
        locale={locale}
      />
    </HydrationBoundary>
  );
}
