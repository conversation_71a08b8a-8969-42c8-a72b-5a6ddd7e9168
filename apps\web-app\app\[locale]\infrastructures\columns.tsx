'use client';

import { EntityTitleLink } from '@/components/entity-title-link/entity-title-link';
import { HeaderRenderer } from '@/components/header-renderer/header-renderer';
import dayjs from '@/lib/dayjs';
import type { Infrastructure } from '@/types/infrastructure';
import type { SupportedLocale } from '@/types/locale';
import type { ColumnDef } from '@tanstack/react-table';
import { InfrastructureActions } from './infrastructure-actions';
const namespace = 'infrastructures';
export const infrastructuresColumns = (
  locale: SupportedLocale,
): ColumnDef<Infrastructure>[] => {
  return [
    {
      accessorKey: 'name',
      cell: ({ row }) => (
        <EntityTitleLink
          id={row.original.id}
          title={row.original.name}
          pathname="/infrastructures/[id]"
        />
      ),
      enableHiding: false,
      enablePinning: true,
      header: ({ header }) => {
        return (
          <HeaderRenderer
            namespace={namespace}
            translationKey={`table.columns.${header.id}`}
          />
        );
      },
      id: 'name',
      size: 280,
    },
    {
      accessorKey: 'address',
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => {
        return (
          <HeaderRenderer
            namespace={namespace}
            translationKey={`table.columns.${header.id}`}
          />
        );
      },
    },
    {
      accessorKey: 'equipmentCount',
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => (
        <HeaderRenderer
          namespace={namespace}
          translationKey={`table.columns.${header.id}`}
        />
      ),
      maxSize: 80,
    },
    {
      accessorKey: 'jurisdiction',
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => (
        <HeaderRenderer
          namespace={namespace}
          translationKey={`table.columns.${header.id}`}
        />
      ),
      maxSize: 350,
      minSize: 280,
    },
    {
      accessorKey: 'scientificDirector',
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => (
        <HeaderRenderer
          namespace={namespace}
          translationKey={`table.columns.${header.id}`}
        />
      ),
      minSize: 200,
    },
    {
      accessorKey: 'statusText',
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => (
        <HeaderRenderer
          namespace={namespace}
          translationKey={`table.columns.${header.id}`}
        />
      ),
      maxSize: 80,
    },
    {
      accessorKey: 'technicalDirector',
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => (
        <HeaderRenderer
          namespace={namespace}
          translationKey={`table.columns.${header.id}`}
        />
      ),
      size: 380,
    },
    {
      accessorKey: 'type',
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => (
        <HeaderRenderer
          namespace={namespace}
          translationKey={`table.columns.${header.id}`}
        />
      ),
      maxSize: 100,
    },
    {
      accessorKey: 'updatedAt',
      cell: (cell) =>
        dayjs(cell.getValue() as string)
          .locale(locale)
          .format('L'),
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => (
        <HeaderRenderer
          namespace={namespace}
          translationKey={`table.columns.${header.id}`}
        />
      ),

      maxSize: 100,
    },
    {
      accessorKey: 'actions',
      cell: ({ row }) => <InfrastructureActions row={row} />,
      enableHiding: false,
      enablePinning: true,
      header: ({ header }) => (
        <HeaderRenderer
          namespace={namespace}
          translationKey={`table.columns.${header.id}`}
        />
      ),
      id: 'actions',
      maxSize: 120,
    },
  ];
};
