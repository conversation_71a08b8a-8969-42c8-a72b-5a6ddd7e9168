import { VendorsList } from '@/app/[locale]/bottin/manufacturiers/vendors-list';
import { getQueryClientOptions } from '@/constants/query-client';
import { getDirectoryListOptions } from '@/hooks/directory/directory-list.options';
import { redirect } from '@/lib/navigation';
import type { BasePageParams } from '@/types/common';
import { auth } from '@rie/auth';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import { headers } from 'next/headers';

export default async function VendorsPage({ params }: BasePageParams) {
  const { locale } = await params;
  const sessionData = await auth.api.getSession({ headers: await headers() });

  if (!sessionData?.user) {
    return redirect({
      href: {
        pathname: '/login',
        query: { from: '/bottin/manufacturiers' },
      },
      locale,
    });
  }

  const queryClient = new QueryClient(getQueryClientOptions(locale));
  await queryClient.prefetchQuery(
    getDirectoryListOptions({
      directoryListKey: 'supplier',
      locale,
      view: 'list',
    }),
  );

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <VendorsList locale={locale} />
    </HydrationBoundary>
  );
}
