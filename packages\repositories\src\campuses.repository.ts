import { DBSchema } from '@rie/db-schema';
import { Campus } from '@rie/domain/aggregates';
import { CampusPersistenceError } from '@rie/domain/errors';
import type { CampusData } from '@rie/domain/types';
import { PgDatabaseLive } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';

export class CampusesRepositoryLive extends Effect.Service<CampusesRepositoryLive>()(
  'CampusesRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const db = yield* PgDatabaseLive;

      const findAllCampuses = () => {
        const getAllCampusesEffect = db.makeQuery((execute) =>
          execute((client) =>
            client.query.campuses.findMany({
              columns: {
                id: true,
                isActive: true,
                sadId: true,
                institutionId: true,
                createdAt: true,
                updatedAt: true,
                modifiedBy: true,
              },
              with: {
                institution: {
                  columns: {
                    id: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        locale: true,
                        name: true,
                      },
                    },
                  },
                },
                translations: {
                  columns: {
                    id: true,
                    dataId: true,
                    locale: true,
                    name: true,
                  },
                },
              },
            }),
          ),
        );

        const rawCampusesEffect = getAllCampusesEffect();
        return pipe(
          rawCampusesEffect,
          Effect.tap((rows) =>
            Effect.sync(() =>
              console.log(
                `[Repo][Campuses] fetched ${rows.length} rows from DB`,
              ),
            ),
          ),
          Effect.flatMap((rawCampuses) =>
            // Use Effect.all to hydrate every raw campus in the array in parallel
            Effect.all(
              rawCampuses.map((rawCampus) =>
                Campus.fromDatabaseData(rawCampus as CampusData),
              ),
            ),
          ),
          Effect.tap((aggregates) =>
            Effect.sync(() =>
              console.log(
                `[Repo][Campuses] hydrated ${aggregates.length} aggregates`,
              ),
            ),
          ),
        );
      };

      const findCampusById = (id: string) => {
        // 1. Create the Effect that fetches the raw data by calling the function returned from makeQuery.
        const getRawCampusEffect = db.makeQuery((execute, id: string) =>
          execute((client) =>
            client.query.campuses.findFirst({
              where: eq(DBSchema.campuses.id, id),
              columns: {
                id: true,
                isActive: true,
                sadId: true,
                institutionId: true,
                createdAt: true,
                updatedAt: true,
                modifiedBy: true,
              },
              with: {
                translations: {
                  columns: {
                    id: true,
                    dataId: true,
                    locale: true,
                    name: true,
                  },
                },
              },
            }),
          ),
        );

        const rawCampusEffect = getRawCampusEffect(id);

        // 2. Now that we have a valid Effect, we pipe it into our hydration logic.
        return pipe(
          rawCampusEffect,
          Effect.flatMap((rawCampus) => {
            if (!rawCampus) {
              return Effect.succeed(null);
            }
            return Campus.fromDatabaseData(rawCampus);
          }),
        );
      };

      const save = (campus: Campus) =>
        pipe(
          campus.toRaw(), // Dehydrate the aggregate to a plain data object, this also validates its state.
          Effect.flatMap((campusData) =>
            pipe(
              db.transaction((tx) =>
                Effect.gen(function* () {
                  // Use Drizzle's insert...onConflictDoUpdate to handle both cases atomically.
                  const [savedCampus] = yield* tx((client) =>
                    client
                      .insert(DBSchema.campuses)
                      .values(campusData)
                      .onConflictDoUpdate({
                        target: DBSchema.campuses.id,
                        set: {
                          isActive: campusData.isActive,
                          sadId: campusData.sadId,
                          institutionId: campusData.institutionId,
                          modifiedBy: campusData.modifiedBy,
                        },
                      })
                      .returning({ id: DBSchema.campuses.id }),
                  );

                  if (!savedCampus) {
                    return yield* Effect.fail(
                      new CampusPersistenceError({
                        reason: 'Failed to save campus to database',
                      }),
                    );
                  }

                  // Replace all translations for this campus.
                  yield* tx((client) =>
                    client
                      .delete(DBSchema.campusesI18N)
                      .where(eq(DBSchema.campusesI18N.dataId, savedCampus.id)),
                  );

                  // Insert translations with the correct dataId and return the result
                  if (campusData.translations.length > 0) {
                    yield* tx((client) =>
                      client.insert(DBSchema.campusesI18N).values(
                        campusData.translations.map((t) => ({
                          ...t,
                          dataId: savedCampus.id, // Use the actual saved campus ID
                        })),
                      ),
                    );
                  }

                  // For new campus creation, just re-fetch to get proper timestamps and IDs
                  // But do it outside the transaction using findCampusById
                  return savedCampus.id;
                }),
              ),
              Effect.flatMap((campusId) => {
                // Now fetch the complete campus outside the transaction
                return findCampusById(campusId);
              }),
              Effect.flatMap((finalCampus) => {
                if (!finalCampus) {
                  return Effect.fail(
                    new CampusPersistenceError({
                      reason: 'Failed to re-fetch campus after save operation',
                    }),
                  );
                }
                return Effect.succeed(finalCampus);
              }),
            ),
          ),
        );

      // — Delete a campus
      const deleteCampus = (id: string) =>
        db.transaction((tx) =>
          Effect.gen(function* () {
            // First delete all translations for this campus
            yield* tx((client) =>
              client
                .delete(DBSchema.campusesI18N)
                .where(eq(DBSchema.campusesI18N.dataId, id)),
            );

            // Then delete the campus itself and return the result
            return yield* tx((client) =>
              client
                .delete(DBSchema.campuses)
                .where(eq(DBSchema.campuses.id, id))
                .returning({ id: DBSchema.campuses.id }),
            );
          }),
        );

      return {
        findAllCampuses,
        findCampusById,
        deleteCampus,
        save,
      } as const;
    }),
  },
) { }
