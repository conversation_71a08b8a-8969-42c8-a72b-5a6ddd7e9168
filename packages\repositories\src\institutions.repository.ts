import { DBSchema } from '@rie/db-schema';
import { Institution } from '@rie/domain/aggregates';
import { InstitutionPersistenceError } from '@rie/domain/errors';
import type { InstitutionData } from '@rie/domain/types';
import { PgDatabaseLive } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';

export class InstitutionsRepositoryLive extends Effect.Service<InstitutionsRepositoryLive>()(
  'InstitutionsRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const dbClient = yield* PgDatabaseLive;

      const findAllInstitutions = () => {
        const getAllInstitutionsEffect = dbClient.makeQuery((execute) =>
          execute((client) =>
            client.query.institutions.findMany({
              columns: {
                id: true,
                isActive: true,
                guidId: true,
                typeId: true,
                createdAt: true,
                updatedAt: true,
                modifiedBy: true,
              },
              with: {
                translations: {
                  columns: {
                    id: true,
                    dataId: true,
                    locale: true,
                    name: true,
                    description: true,
                    acronyms: true,
                    otherNames: true,
                  },
                },
                type: {
                  columns: {
                    id: true,
                    uid: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        id: true,
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
              },
            }),
          ),
        );

        const rawInstitutionsEffect = getAllInstitutionsEffect();
        return pipe(
          rawInstitutionsEffect,
          Effect.tap((rows) =>
            Effect.sync(() =>
              console.log(
                `[Repo][Institutions] fetched ${rows.length} rows from DB`,
              ),
            ),
          ),
          Effect.flatMap((rawInstitutions) =>
            // Use Effect.all to hydrate every raw institution in the array in parallel
            Effect.all(
              rawInstitutions.map((rawInstitution) =>
                Institution.fromDatabaseData(rawInstitution as InstitutionData),
              ),
            ),
          ),
          Effect.tap((aggregates) =>
            Effect.sync(() =>
              console.log(
                `[Repo][Institutions] hydrated ${aggregates.length} aggregates`,
              ),
            ),
          ),
        );
      };

      const findInstitutionById = (id: string) => {
        // 1. Create the Effect that fetches the raw data by calling the function returned from makeQuery.
        const getRawInstitutionEffect = dbClient.makeQuery((execute, id: string) =>
          execute((client) =>
            client.query.institutions.findFirst({
              where: eq(DBSchema.institutions.id, id),
              columns: {
                id: true,
                isActive: true,
                guidId: true,
                typeId: true,
                createdAt: true,
                updatedAt: true,
                modifiedBy: true,
              },
              with: {
                translations: {
                  columns: {
                    id: true,
                    dataId: true,
                    locale: true,
                    name: true,
                    description: true,
                    acronyms: true,
                    otherNames: true,
                  },
                },
                type: {
                  columns: {
                    id: true,
                    uid: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        id: true,
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
              },
            }),
          ),
        );

        const rawInstitutionEffect = getRawInstitutionEffect(id);

        // 2. Now that we have a valid Effect, we pipe it into our hydration logic.
        return pipe(
          rawInstitutionEffect,
          Effect.flatMap((rawInstitution) => {
            if (!rawInstitution) {
              return Effect.succeed(null);
            }
            return Institution.fromDatabaseData(rawInstitution as InstitutionData);
          }),
        );
      };

      // — Save (create or update) an institution aggregate
      const save = (institution: Institution) =>
        pipe(
          institution.toRaw(), // Dehydrate the aggregate to a plain data object, this also validates its state.
          Effect.flatMap((institutionData) =>
            pipe(
              dbClient.transaction((tx) =>
                Effect.gen(function* () {
                  // Use Drizzle's insert...onConflictDoUpdate to handle both cases atomically.
                  const [savedInstitution] = yield* tx((client) =>
                    client
                      .insert(DBSchema.institutions)
                      .values(institutionData)
                      .onConflictDoUpdate({
                        target: DBSchema.institutions.id,
                        set: {
                          isActive: institutionData.isActive,
                          guidId: institutionData.guidId,
                          typeId: institutionData.typeId,
                          modifiedBy: institutionData.modifiedBy,
                        },
                      })
                      .returning({ id: DBSchema.institutions.id }),
                  );

                  if (!savedInstitution) {
                    return yield* Effect.fail(
                      new InstitutionPersistenceError({
                        message: 'Failed to save institution to database',
                      }),
                    );
                  }

                  // Replace all translations for this institution.
                  yield* tx((client) =>
                    client
                      .delete(DBSchema.institutionsI18N)
                      .where(eq(DBSchema.institutionsI18N.dataId, savedInstitution.id)),
                  );

                  // Insert translations with the correct dataId and return the result
                  if (institutionData.translations.length > 0) {
                    yield* tx((client) =>
                      client.insert(DBSchema.institutionsI18N).values(
                        institutionData.translations.map((t) => ({
                          ...t,
                          dataId: savedInstitution.id, // Use the actual saved institution ID
                        })),
                      ),
                    );
                  }

                  return savedInstitution.id;
                }),
              ),
              Effect.flatMap((institutionId) => {
                // Now fetch the complete institution outside the transaction
                return findInstitutionById(institutionId);
              }),
              Effect.flatMap((finalInstitution) => {
                if (!finalInstitution) {
                  return Effect.fail(
                    new InstitutionPersistenceError({
                      message: 'Failed to re-fetch institution after save operation',
                    }),
                  );
                }
                return Effect.succeed(finalInstitution);
              }),
            ),
          ),
        );



      // — Delete an institution
      const deleteInstitution = (id: string) =>
        dbClient.transaction((tx) =>
          Effect.gen(function* () {
            // First delete all translations for this institution
            yield* tx((client) =>
              client
                .delete(DBSchema.institutionsI18N)
                .where(eq(DBSchema.institutionsI18N.dataId, id)),
            );

            // Then delete the institution itself and return the result
            return yield* tx((client) =>
              client
                .delete(DBSchema.institutions)
                .where(eq(DBSchema.institutions.id, id))
                .returning({ id: DBSchema.institutions.id }),
            );
          }),
        );

      // — Find all institutions with raw data (for serialization)
      const findAllInstitutionsRaw = () => {
        const getAllInstitutionsEffect = dbClient.makeQuery((execute) =>
          execute((client) =>
            client.query.institutions.findMany({
              columns: {
                id: true,
                isActive: true,
                guidId: true,
                typeId: true,
                createdAt: true,
                updatedAt: true,
                modifiedBy: true,
              },
              with: {
                translations: {
                  columns: {
                    id: true,
                    dataId: true,
                    locale: true,
                    name: true,
                    description: true,
                    acronyms: true,
                    otherNames: true,
                  },
                },
                type: {
                  columns: {
                    id: true,
                    uid: true,
                  },
                  with: {
                    translations: {
                      columns: {
                        id: true,
                        locale: true,
                        name: true,
                        description: true,
                      },
                    },
                  },
                },
              },
            }),
          ),
        );

        return getAllInstitutionsEffect();
      };

      return {
        findAllInstitutions,
        findAllInstitutionsRaw,
        findInstitutionById,
        save,
        deleteInstitution,
      } as const;
    }),
  },
) { }
