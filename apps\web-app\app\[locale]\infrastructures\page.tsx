import { InfrastructurePage } from '@/app/[locale]/infrastructures/infrastructure-page';
import { getQueryClientOptions } from '@/constants/query-client';
import { defaultRieServiceParams } from '@/constants/rie-client';
import { infiniteInfrastructuresOptions } from '@/hooks/infrastructure/useGetInfiniteInfrastructuresOptions';
import { infrastructuresListOptions } from '@/hooks/infrastructure/useGetInfrastructuresList';
import { allFunctionalitiesOptions } from '@/hooks/useGetAllFunctionalities';
import { getSearchParamsAndUser } from '@/server/common.server';
import type { BasePageParams } from '@/types/common';
import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from '@tanstack/react-query';

export default async function InfrastructuresPage(props: BasePageParams) {
  const params = await props.params;

  const { locale } = params;

  const { searchParams, userId } = await getSearchParamsAndUser();

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  await Promise.all([
    queryClient.prefetchInfiniteQuery(
      infiniteInfrastructuresOptions({
        params: defaultRieServiceParams(locale),
        queryParams: searchParams ?? '',
        viewOwnInfrastructures: false,
      }),
    ),
    queryClient.prefetchQuery(
      infrastructuresListOptions({
        pageParam: 0,
        params: defaultRieServiceParams(locale),
        queryParams: searchParams ?? '',
        viewOwnInfrastructures: false,
      }),
    ),
    ...(userId ? [allFunctionalitiesOptions(userId)] : []),
  ]);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <InfrastructurePage locale={locale} />
    </HydrationBoundary>
  );
}
