import { DbInfrastructureI18NSelectSchema } from '@rie/db-schema/entity-schemas';
import * as Schema from 'effect/Schema';
import {
  optionalFieldWth150MaxLengthSchema,
  optionalFieldWth1500MaxLengthSchema,
} from './base.schema';
import { LocaleSchema } from './query.schema';

// — Translation schema for related entities
const RelatedEntityTranslationSchema = Schema.Struct({
  id: Schema.String,
  locale: Schema.String,
  name: Schema.NullOr(Schema.String),
});

const InfrastructureEntityWithTranslationsSchema = Schema.Struct({
  id: Schema.String,
  translations: Schema.Array(RelatedEntityTranslationSchema),
});

// — Equipment minimal schema (for related equipments)
const EquipmentMinimalSchema = Schema.Struct({
  id: Schema.String,
  translations: Schema.Array(RelatedEntityTranslationSchema),
});

const AddressSchema = Schema.Struct({
  id: Schema.String,
  addressType: Schema.String,
  campusAddressId: Schema.NullOr(Schema.String),
  civicAddressId: Schema.NullOr(Schema.String),
});

const ManagerSchema = Schema.Struct({
  id: Schema.String,
  firstName: Schema.String,
  lastName: Schema.String,
});

// — Full Infrastructure shape (matches findInfrastructureById return)
// This schema represents the complete infrastructure object as returned by the repository
// including all related entities with their translations
export const InfrastructureDataSchema = Schema.Struct({
  // Core infrastructure fields
  id: Schema.String,
  guidId: Schema.NullOr(Schema.String),
  addressId: Schema.NullOr(Schema.String),
  website: Schema.NullOr(Schema.String),
  isFeatured: Schema.Boolean,
  visibilityId: Schema.String,
  createdAt: Schema.String,
  updatedAt: Schema.String,
  modifiedBy: Schema.NullOr(Schema.String),
  // Related entities (populated by repository joins)
  scientificManagers: Schema.Array(ManagerSchema),
  operationalManagers: Schema.Array(ManagerSchema),
  sstManagers: Schema.Array(ManagerSchema),
  address: Schema.NullOr(AddressSchema),
  type: InfrastructureEntityWithTranslationsSchema,
  status: InfrastructureEntityWithTranslationsSchema,
  unit: Schema.NullOr(InfrastructureEntityWithTranslationsSchema),
  translations: Schema.Array(DbInfrastructureI18NSelectSchema.omit('dataId')),
  visibility: InfrastructureEntityWithTranslationsSchema,
});

// — Infrastructure with related equipments schema (extends InfrastructureDataSchema)
// This schema represents the infrastructure object with its related equipments
// as returned by findInfrastructureWithRelatedEquipmentsById
export const InfrastructureWithRelatedEquipmentsSchema = Schema.Struct({
  ...InfrastructureDataSchema.fields,
  equipments: Schema.Array(EquipmentMinimalSchema),
});

// — Type exports
export type InfrastructureWithRelatedEquipments = Schema.Schema.Type<
  typeof InfrastructureWithRelatedEquipmentsSchema
>;

// — Translation input schema
export const InfrastructureI18NInputSchema = Schema.Struct({
  locale: LocaleSchema,
  name: optionalFieldWth150MaxLengthSchema('Name'),
  description: optionalFieldWth1500MaxLengthSchema('Description'),
  otherNames: optionalFieldWth150MaxLengthSchema('Other Names'),
  acronyms: optionalFieldWth150MaxLengthSchema('Acronyms'),
});

// — Input (create/update) shape
export const InfrastructureInputSchema = Schema.Struct({
  guidId: Schema.NullishOr(Schema.String),
  isActive: Schema.Boolean,
  typeId: Schema.NonEmptyString,
  statusId: Schema.NonEmptyString,
  website: Schema.NullishOr(Schema.String),
  isFeatured: Schema.Boolean,
  visibilityId: Schema.NonEmptyString,
  unitId: Schema.NullishOr(Schema.String),
  modifiedBy: Schema.NullishOr(Schema.String),
  addressId: Schema.NonEmptyString,
  translations: Schema.Array(InfrastructureI18NInputSchema),
});
