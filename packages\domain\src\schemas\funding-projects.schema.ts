import {
  DbFundingProjectI18NSelectSchema,
  DbFundingProjectInputSchema,
  DbFundingProjectSelectSchema,
} from '@rie/db-schema/entity-schemas';
import type { DbFundingProjectI18N } from '@rie/db-schema/entity-types';
import * as Schema from 'effect/Schema';
import {
  optionalFieldWth1500MaxLengthSchema,
  requiredFieldWth150MaxLengthSchema,
} from './base.schema';
import { LocaleSchema } from './query.schema';
import { createDbTranslationSchema } from './translation.schema';

// — Full Funding Project shape
export const FundingProjectSchema = Schema.Struct({
  ...DbFundingProjectSelectSchema.omit('modifiedBy').fields,
  modifiedBy: Schema.NullishOr(Schema.String),
  translations: Schema.Array(
    DbFundingProjectI18NSelectSchema.omit('id', 'dataId'),
  ),
});

// — Translation input schema
export const FundingProjectI18NInputSchema = Schema.Struct({
  locale: LocaleSchema,
  name: requiredFieldWth150MaxLengthSchema('Name'),
  description: optionalFieldWth1500MaxLengthSchema('Description'),
});

// — Funding Project List view schema (for directory table)
export const FundingProjectListSchema = Schema.Struct({
  id: Schema.String,
  text: Schema.String, // nom par défaut depuis traductions
  titulaire: Schema.NullishOr(Schema.String), // titulaire
  infrastructure: Schema.NullishOr(Schema.String), // infrastructure
  lastUpdatedAt: Schema.String,
  // Champs internes pour les actions
  holderId: Schema.NullishOr(Schema.String),
  typeId: Schema.NullishOr(Schema.String),
  fciId: Schema.NullishOr(Schema.String),
  synchroId: Schema.NullishOr(Schema.String),
  obtainingYear: Schema.NullishOr(Schema.Number),
  endDate: Schema.NullishOr(Schema.String),
});

// — Funding Project Select view schema
export const FundingProjectSelectSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

// — Funding Project Edit view schema
export const FundingProjectEditSchema = Schema.Struct({
  id: Schema.String,
  holderId: Schema.NullishOr(Schema.String),
  typeId: Schema.NullishOr(Schema.String),
  fciId: Schema.NullishOr(Schema.String),
  synchroId: Schema.NullishOr(Schema.String),
  obtainingYear: Schema.NullishOr(Schema.Number),
  endDate: Schema.NullishOr(Schema.String),
  translations: Schema.Array(FundingProjectI18NInputSchema),
});

// — Funding Project Detail view schema (same as list for now)
export const FundingProjectDetailSchema = FundingProjectListSchema;

// — Funding Project Form schema (for the frontend form)
export const FundingProjectFormSchema = Schema.Struct({
  id: Schema.optional(Schema.String),
  name: Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      value: Schema.String,
    }),
  ),
  description: Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      value: Schema.String,
    }),
  ),
  holder: Schema.Struct({
    label: Schema.NullishOr(Schema.String),
    value: Schema.NullishOr(Schema.String),
  }),
  fundingType: Schema.Struct({
    label: Schema.NullishOr(Schema.String),
    value: Schema.NullishOr(Schema.String),
  }),
  obtainingYear: Schema.optional(Schema.Number),
  endDate: Schema.optional(Schema.String),
  fciId: Schema.optional(Schema.String),
  synchroId: Schema.optional(Schema.String),
  purchasedEquipment: Schema.Array(
    Schema.Struct({
      label: Schema.String,
      value: Schema.String,
    }),
  ),
  associateResearchers: Schema.Array(
    Schema.Struct({
      label: Schema.String,
      value: Schema.String,
    }),
  ),
  financedInfrastructures: Schema.Array(
    Schema.Struct({
      label: Schema.String,
      value: Schema.String,
    }),
  ),
});

// — Input (create/update) shape
export const FundingProjectInputSchema = Schema.Struct({
  ...DbFundingProjectInputSchema.omit('id').fields,
  translations: Schema.Array(FundingProjectI18NInputSchema),
});

// API Input Schema - for external API validation (excludes dataId)
export const DbFundingProjectI18NInputSchema = createDbTranslationSchema<DbFundingProjectI18N>({
  fields: {
    name: {
      required: true,
      maxLength: 150,
    },
    description: {
      required: false,
      maxLength: 1500,
    },
  },
});

// Domain Creation Schema - for domain object creation (includes dataId)
export const DbFundingProjectI18NCreationSchema = Schema.extend(
  DbFundingProjectI18NInputSchema,
  Schema.Struct({
    dataId: Schema.String,
  }),
);

// Full Data Schema - for complete database records (includes id and dataId)
export const DbFundingProjectI18NDataSchema = Schema.extend(
  DbFundingProjectI18NCreationSchema,
  Schema.Struct({
    id: Schema.String,
  }),
);

// — Database schema (for serializers)
export const DbFundingProjectSchema = FundingProjectSchema;

export const DbFundingProjectDataSchema = Schema.Struct({
  ...DbFundingProjectSchema.fields,
  translations: Schema.Array(DbFundingProjectI18NDataSchema),
});