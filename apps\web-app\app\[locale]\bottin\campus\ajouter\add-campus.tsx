'use client';

import { CampusForm } from '@/app/[locale]/bottin/campus/form/campus-form';
import { campusFormDefaultValues } from '@/constants/directory/campus';
import { useCreateCampus } from '@/hooks/directory/campuses.hook';
import type { CampusFormSchema } from '@/schemas/bottin/campus-form-schema';
import type { SupportedLocale } from '@/types/locale';

type AddCampusProps = {
  locale: SupportedLocale;
  formSections: Record<string, string>;
};
export const AddCampus = ({ locale, formSections }: AddCampusProps) => {
  const { mutate, status } = useCreateCampus();
  const onSubmit = (data: CampusFormSchema) => {
    mutate(data);
  };

  return (
    <CampusForm
      defaultValues={campusFormDefaultValues(locale)}
      formSections={formSections}
      onSubmit={onSubmit}
      status={status}
    />
  );
};
