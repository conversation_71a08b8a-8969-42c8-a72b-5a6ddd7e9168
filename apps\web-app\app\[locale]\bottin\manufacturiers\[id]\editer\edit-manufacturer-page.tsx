'use client';
import { ManufacturerForm } from '@/app/[locale]/bottin/manufacturiers/form/manufacturer-form';
import { LoadingResource } from '@/components/loading-resource/loading-resource';
import { useGetDirectoryById } from '@/hooks/directory/directory-list.hook';
import { useUpdateVendor } from '@/hooks/directory/vendors.hook';
import type { VendorFormSchema } from '@/schemas/bottin/vendor-form-schema';
import type {
  ManufacturerFormSectionKey
} from '@/types/directory/manufacturer';
import { SupportedLocale } from '@/types/locale';

type EditionManufacturerPageParams = {
  formSections: Record<ManufacturerFormSectionKey, string>;
  id: string;
  locale: SupportedLocale;
};
export default function EditionManufacturerPage({
  formSections,
  id,
}: EditionManufacturerPageParams) {
  const {
      data: vendor,
      error,
      isPending,
    } = useGetDirectoryById<'supplier', 'edit'>({
      directoryListKey: 'supplier',
      id,
      view: 'edit',
    });
  
    if (isPending) {
      return <LoadingResource />;
    }
  
    if (error) {
      return <div className="p-4 text-red-500">Erreur : {error.message}</div>;
    }
  
    const { mutate, status } = useUpdateVendor();
    const onSubmit = (data: VendorFormSchema) => {
      mutate({ id, payload: data });
    };
  return (
    <ManufacturerForm
      defaultValues={vendor}
      formSections={formSections}
      onSubmit={onSubmit}
      status={status}
    />
  );
}
