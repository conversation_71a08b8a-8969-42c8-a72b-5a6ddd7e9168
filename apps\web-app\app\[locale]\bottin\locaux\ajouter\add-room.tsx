'use client';

import { RoomForm } from '@/app/[locale]/bottin/locaux/form/room-form';
import { roomFormDefaultValues } from '@/constants/directory/room';
import { useCreateRoom } from '@/hooks/directory/rooms.hook';
import type { RoomFormSchema } from '@/schemas/bottin/room-form-schema';

type AddRoomProps = {
  formSections: Record<string, string>;
};

export const AddRoom = ({ formSections }: AddRoomProps) => {
  const { mutate, status } = useCreateRoom();
  const onSubmit = (data: RoomFormSchema) => {
    mutate(data);
  };

  return (
    <RoomForm
      defaultValues={roomFormDefaultValues()}
      formSections={formSections}
      onSubmit={onSubmit}
      status={status}
    />
  );
};
