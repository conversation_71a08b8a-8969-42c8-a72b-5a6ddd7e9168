import * as fs from 'node:fs/promises';
import { INPUT_PATH, OUTPUT_PATH } from '../constants';
import { logConverterRecordCounts } from '../converter-validator';
import type {
  Mapping,
  MySqlI18NBaseReturn,
  PostgresI18NBaseReturn,
} from '../types';
import { BaseConverter } from './base-converter';

export class ApplicationSectorMigrationConverter extends BaseConverter {
  private applicationSectorMappings: Mapping[] = [];

  private parseInsertStatement(
    sqlStatement: string,
    sqlContent: string,
  ): MySqlI18NBaseReturn[] {
    const values = this.extractValuesFromInsertStatement(
      sqlStatement,
      sqlContent,
      'secteur_application',
    );

    return [
      {
        id: Number.parseInt(values.id),
      },
    ];
  }

  private convertToPostgres(
    mysqlApplicationSector: MySqlI18NBaseReturn,
  ): PostgresI18NBaseReturn {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.applicationSectorMappings.push({
      mysqlId: mysqlApplicationSector.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
    };
  }

  async convertFile(): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(INPUT_PATH, 'utf8');

      // Extract INSERT statements for secteur_application table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'secteur_application',
      );

      if (insertStatements.length === 0) {
        console.log('No secteur_application INSERT statements found.');
        return;
      }

      const allPostgresApplicationSectors: PostgresI18NBaseReturn[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlApplicationSectors = this.parseInsertStatement(
          statement,
          sqlContent,
        );
        const postgresApplicationSectors = mysqlApplicationSectors.map(
          (sector) => this.convertToPostgres(sector),
        );
        allPostgresApplicationSectors.push(...postgresApplicationSectors);
      }

      // Generate output with both inserts and mappings
      const postgresInsertsWithMappings =
        this.generatePostgresBaseTableInsertWithMappings(
          allPostgresApplicationSectors,
          'application_sectors',
          'Application Sector Inserts',
        );

      // Create the output directory if it doesn't exist
      const outputDir = OUTPUT_PATH.substring(0, OUTPUT_PATH.lastIndexOf('/'));
      await this.safeMkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await this.safeAppendFile(OUTPUT_PATH, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.applicationSectorMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'secteur_application', postgres: 'application_sectors' },
        idMappings,
      );

      console.log('Conversion completed successfully!');

      // Validate the conversion - this will log any record count differences
      await logConverterRecordCounts('ApplicationSectorMigrationConverter', [
        { mysql: 'secteur_application', postgres: 'application_sectors' },
      ]);
      console.log(
        `- Found ${allPostgresApplicationSectors.length} application_sectors records`,
      );
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
