import type { buildingFormSections } from '@/constants/directory/building';
import type { campusFormSections } from '@/constants/directory/campus';
import type {
  EntityBase,
  EntityLocaleDictionary,
  InfrastructureType,
} from '@/types/common';
import type { BaseEntity } from '@/types/directory/directory';
import type { Affiliations, Territory } from '@/types/infrastructure';

export type CampusPostPayload = {
  etablissement: {
    id: string;
  };
  noms: EntityLocaleDictionary;
  pseudonyme: string;
};

export type CampusFormSectionKey = keyof typeof campusFormSections;

export type BuildingPostPayload = {
  batimentEmplacements?: (
    | {
      batiment: {
        id: string;
      };
    }
    | Territory
  )[];
  campus: {
    id: string;
  };
  juridiction: {
    id: string;
  };
  noms: EntityLocaleDictionary;
  pseudonymes: EntityLocaleDictionary;
};

export type EstablishmentFull = {
  acronym: null | string;
  acronyms: EntityLocaleDictionary;
  createdAt: string;
  dateBegin: null | string;
  dateEnd: null | string;
  descriptions?: EntityLocaleDictionary; //Not in etablissement get
  id: string;
  lastUpdatedAt: null | string;
  names: EntityLocaleDictionary;
  organizationId: number;
  parent: EstablishmentFull | null;
  pseudonymes: EntityLocaleDictionary;
  text: string;
  typeContenu?: string; //Not in etablissement get
  typeEtablissement: InfrastructureType;
  uid: string;
  affiliatedPersons?: Affiliations[];
};
export type CampusFull = {
  createdAt?: null | string;
  etablissement: Pick<EstablishmentFull, 'uid' | 'id' | 'text'> & {
    names: EntityLocaleDictionary;
  }; // TODO: Validate this type, we are most likely missing `pseudonymes`
  id: string;
  lastUpdatedAt?: null | string;
  nom: string;
  noms: EntityLocaleDictionary;
  pseudonyme?: null;
  slug?: string;
  text: string;
  uid: string;
};

export type Campus = {
  jurisdiction: string;
} & BaseEntity;

export type BuildingFull = {
  campus: CampusFull;
  createdAt?: string;
  id: string;
  juridiction: EstablishmentFull;
  lastUpdatedAt?: null | string;
  nom: string;
  noms: EntityLocaleDictionary;
  pseudonyme: null | string;
  pseudonymes?: EntityLocaleDictionary;
  slug: null | string;
  text: string;
  uid: string;
  emplacement?: Territory;
};

export type RoomFull = {
  batiment: BuildingFull;
  id: string;
  juridiction: EstablishmentFull;
  numero: string;
  pseudonyme: null | string;
  slug: null | string;
  superficie: string;
  portantePlancher: string | null;
  categories: Omit<EntityBase, 'descriptions'>[];
} & BaseEntity;

export type BuildingFormSectionKey = keyof typeof buildingFormSections;
