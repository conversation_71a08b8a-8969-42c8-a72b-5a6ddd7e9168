import * as fs from 'node:fs/promises';
import { INPUT_PATH, OUTPUT_PATH } from '../constants';
import { logConverterRecordCounts } from '../converter-validator';
import type {
  Mapping,
  MySqlI18NBaseReturn,
  PostgresI18NBaseReturn,
} from '../types';
import { BaseConverter } from './base-converter';

export class FundingProjectTypeMigrationConverter extends BaseConverter {
  private fundingProjectTypeMappings: Mapping[] = [];

  private parseInsertStatement(
    sqlStatement: string,
    sqlContent: string,
  ): MySqlI18NBaseReturn[] {
    const values = this.extractValuesFromInsertStatement(
      sqlStatement,
      sqlContent,
      'type_projet_financement',
    );

    return [
      {
        id: Number.parseInt(values.id),
      },
    ];
  }

  private convertToPostgres(
    mysqlFundingProjectType: MySqlI18NBaseReturn,
  ): PostgresI18NBaseReturn {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.fundingProjectTypeMappings.push({
      mysqlId: mysqlFundingProjectType.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
    };
  }

  async convertFile(): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(INPUT_PATH, 'utf8');

      // Extract INSERT statements for type_projet_financement table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'type_projet_financement',
      );

      if (insertStatements.length === 0) {
        console.log('No type_projet_financement INSERT statements found.');
        return;
      }

      const allPostgresFundingProjectTypes: PostgresI18NBaseReturn[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlFundingProjectTypes = this.parseInsertStatement(
          statement,
          sqlContent,
        );
        const postgresFundingProjectTypes = mysqlFundingProjectTypes.map(
          (fpt) => this.convertToPostgres(fpt),
        );
        allPostgresFundingProjectTypes.push(...postgresFundingProjectTypes);
      }

      // Generate output with both inserts and mappings
      const postgresInsertsWithMappings =
        this.generatePostgresBaseTableInsertWithMappings(
          allPostgresFundingProjectTypes,
          'funding_project_types',
          'Funding Project Type Inserts',
        );

      // Create the output directory if it doesn't exist
      const outputDir = OUTPUT_PATH.substring(0, OUTPUT_PATH.lastIndexOf('/'));
      await this.safeMkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await this.safeAppendFile(OUTPUT_PATH, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.fundingProjectTypeMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'type_projet_financement', postgres: 'funding_project_types' },
        idMappings,
      );

      console.log('Conversion completed successfully!');

      // Validate the conversion - this will log any record count differences
      await logConverterRecordCounts('FundingProjectTypeMigrationConverter', [
        { mysql: 'type_projet_financement', postgres: 'funding_project_types' },
      ]);
      console.log(
        `- Found ${allPostgresFundingProjectTypes.length} funding_project_types records`,
      );
      console.log(`- PostgreSQL inserts written to: ${OUTPUT_PATH}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
