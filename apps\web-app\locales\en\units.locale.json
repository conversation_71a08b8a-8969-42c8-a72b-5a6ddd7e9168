{"table": {"columns": {"name": "Name", "acronym": "Acronym", "parent": "Parent", "createdAt": "Created", "lastUpdatedAt": "Last Updated", "actions": "Actions"}}, "form": {"sections": {"description": {"title": "Description", "generalInfo": {"title": "General information", "fields": {"unitType": {"label": "Unit type", "options": {"Administrative": "Administrative unit", "Research": "Research unit"}}, "acronym": {"label": "Acronym", "error": {"max": "Acronym must be less than {max} characters"}}, "name": {"label": "Name in {locale}", "error": {"required": "Name is required", "max": "Name can't have more than {max} characters"}}, "alias": {"label": "<PERSON>as in {locale}", "tooltip": "Other or former names", "error": {"max": "<PERSON><PERSON> can't have more than {max} characters"}}, "parentUnit": {"label": "Parent unit", "error": {"required": "Parent unit is required"}}, "relatedOrganizations": {"label": "Related organizations"}}}}, "affiliations": {"title": "Affiliations", "fields": {}}}}}