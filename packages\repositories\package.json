{"name": "@rie/repositories", "version": "1.0.0", "type": "module", "description": "Repositories package", "main": "./build/index.js", "module": "./build/index.js", "types": "./build/dts/index.d.ts", "exports": {".": {"types": "./build/dts/index.d.ts", "import": "./build/index.js", "require": "./build/index.js"}}, "files": ["build", "build/dts"], "scripts": {"build": "tsc -b tsconfig.build.json", "dev": "tsc -b tsconfig.src.json -w", "type-check": "tsc -b tsconfig.build.json", "lint": "pnpm biome check --write"}, "dependencies": {"@rie/biome-config": "workspace:*", "@rie/config": "workspace:*", "@rie/constants": "workspace:*", "@rie/db-schema": "workspace:*", "@rie/domain": "workspace:*", "@rie/postgres-db": "workspace:*", "remeda": "^2.23.1"}, "devDependencies": {"@types/bun": "^1.2.21", "typescript": "^5.9.2"}, "peerDependencies": {"drizzle-orm": "^0.44.5", "effect": "^3.17.13"}}