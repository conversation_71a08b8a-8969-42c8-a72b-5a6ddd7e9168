import {
  DbRoomInputSchema,
  DbRoomSelectSchema,
} from '@rie/db-schema/entity-schemas';
import * as Schema from 'effect/Schema';

// Database schema for Room data (raw from database)
export const DbRoomDataSchema = Schema.Struct({
  id: Schema.String,
  number: Schema.String,
  area: Schema.NullOr(Schema.Number),
  floorLoad: Schema.NullOr(Schema.Number),
  buildingId: Schema.NullOr(Schema.String),
  isActive: Schema.Boolean,
  createdAt: Schema.String,
  updatedAt: Schema.String,
  modifiedBy: Schema.NullOr(Schema.String),
});

// — Full Room shape (rooms don't have translations in the DB schema)
export const RoomSchema = Schema.Struct({
  ...DbRoomSelectSchema.omit('modifiedBy').fields,
  modifiedBy: Schema.NullishOr(Schema.String),
});

// — Database schema (for serializers)
export const DbRoomSchema = RoomSchema;

// — Room List view schema (for directory table)
export const RoomListSchema = Schema.Struct({
  id: Schema.String,
  numero: Schema.String, // numéro de la salle
  building: Schema.NullishOr(Schema.String), // nom du bâtiment
  jurisdiction: Schema.NullishOr(Schema.String), // juridiction
  lastUpdatedAt: Schema.String,
  // Champs internes pour les actions
  area: Schema.NullishOr(Schema.Number),
  floorLoad: Schema.NullishOr(Schema.Number),
  buildingId: Schema.NullishOr(Schema.String),
  isActive: Schema.NullishOr(Schema.Boolean),
});

// — Room Select view schema
export const RoomSelectSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

// — Room Edit view schema (form-compatible format)
export const RoomEditSchema = Schema.Struct({
  id: Schema.optional(Schema.String), // Optional for create, required for update
  number: Schema.String,
  alias: Schema.optional(Schema.String),
  area: Schema.NullishOr(Schema.Number),
  floorLoad: Schema.NullishOr(Schema.Number),
  capacity: Schema.optional(Schema.Number),
  buildingId: Schema.NullishOr(Schema.String),
  buildingName: Schema.NullishOr(Schema.String), // for form building selection
  jurisdictionId: Schema.NullishOr(Schema.String),
  jurisdictionName: Schema.NullishOr(Schema.String), // for form jurisdiction selection
  isActive: Schema.NullishOr(Schema.Boolean),
  categories: Schema.mutable(Schema.Array(
    Schema.Struct({
      value: Schema.String,
      label: Schema.String,
    }),
  )),
});

export const RoomFormSchema = Schema.Struct({
  id: Schema.optional(Schema.String), // Optional for create, required for update
  roomNumber: Schema.String,
  alias: Schema.String,
  area: Schema.Number,
  capacity: Schema.Number,
  building: Schema.Struct({
    label: Schema.Union(Schema.Null, Schema.String),
    value: Schema.Union(Schema.Null, Schema.String),
  }),
  jurisdiction: Schema.Struct({
    label: Schema.Union(Schema.Null, Schema.String),
    value: Schema.Union(Schema.Null, Schema.String),
  }),
  categories: Schema.mutable(Schema.Array(
    Schema.Struct({
      value: Schema.String,
      label: Schema.String,
    }),
  )),
});

// — Room Detail view schema (same as list for now)
export const RoomDetailSchema = RoomListSchema;

// — Input (create/update) shape
export const RoomInputSchema = Schema.Struct({
  ...DbRoomInputSchema.omit('id').fields,
});
