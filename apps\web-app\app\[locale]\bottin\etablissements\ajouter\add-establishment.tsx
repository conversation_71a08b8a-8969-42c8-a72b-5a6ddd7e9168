'use client';

import { EstablishmentForm } from '@/app/[locale]/bottin/etablissements/form/establishment-form';
import { establishmentFormDefaultValues } from '@/constants/directory/establishment';
import { useCreateEstablishment } from '@/hooks/directory/establishments.hook';
import type { EstablishmentFormSchema } from '@/schemas/bottin/establishment-form-schema';
import type { SupportedLocale } from '@/types/locale';

type AddEstablishmentProps = {
  locale: SupportedLocale;
  formSections: Record<string, string>;
};

export const AddEstablishment = ({
  locale,
  formSections,
}: AddEstablishmentProps) => {
  const { mutate, status } = useCreateEstablishment();
  const onSubmit = (data: EstablishmentFormSchema) => {
    mutate(data);
  };

  return (
    <EstablishmentForm
      defaultValues={establishmentFormDefaultValues(locale)}
      formSections={formSections}
      onSubmit={onSubmit}
      status={status}
    />
  );
};
