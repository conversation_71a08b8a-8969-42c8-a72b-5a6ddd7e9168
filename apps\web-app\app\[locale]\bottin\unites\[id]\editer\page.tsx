import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { unitFormSections } from '@/constants/directory/unit';
import { getQueryClientOptions } from '@/constants/query-client';
import { getMultipleControlledListsSelectOptions } from '@/hooks/controlled-list/controlled-list.options';
import { getDirectoryByIdOptions, getDirectoryListOptions } from '@/hooks/directory/directory-list.options';
import { redirect } from '@/lib/navigation';
import type { ControlledListKey, DirectoryListKey, PageDetailsParams } from '@/types/common';
import { auth } from '@rie/auth';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import { headers } from 'next/headers';
import { notFound } from 'next/navigation';
import EditionUnitPage from './edit-unit-page';

type EditUnitPageParams = PageDetailsParams;
export default async function EditUnitPage(props: EditUnitPageParams) {
  const params = await props.params;

  const { id, locale } = params;

  const sessionData = await auth.api.getSession({ headers: await headers() });

  if (!sessionData?.user) {
    return redirect({
      href: {
        pathname: '/login',
        query: { from: `/bottin/unites/${id}/editer` },
      },
      locale,
    });
  }

  const formSections = await getFormSections({
    resourceName: 'units',
    sections: unitFormSections,
  });

  const queryClient = new QueryClient(getQueryClientOptions(locale));
  const t0 = performance.now();

  const controlledLists: ControlledListKey[] = [
    'unitType',
  ];
  const directoryLists: DirectoryListKey[] = [
    'organisation',
    'person',
  ];

  const unit = await Promise.all([
    queryClient.fetchQuery(
      getDirectoryByIdOptions({ 
        directoryListKey: 'unit', 
        id, 
        view: 'edit' 
      }),
    ),
    // Prefetch toutes les listes contrôlées en une seule requête
    queryClient.prefetchQuery(
      getMultipleControlledListsSelectOptions({
        controlledListKeys: controlledLists,
        locale,
      }),
    ),
    // Prefetch les organisations et personnes via l'endpoint bottin
    ...directoryLists.map((directoryListKey) =>
      queryClient.prefetchQuery(
        getDirectoryListOptions({
          directoryListKey,
          locale,
          view: 'select',
        }),
      ),
    ),
  ]);

  const t1 = performance.now();

  console.log(
    `Call to fetch controlledLists "organization", "unitType", "person" and "unit" took ${t1 - t0} milliseconds.`,
  );
  if (!unit) {
    return notFound();
  }

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <EditionUnitPage formSections={formSections} id={id} locale={locale} />
    </HydrationBoundary>
  );
}
