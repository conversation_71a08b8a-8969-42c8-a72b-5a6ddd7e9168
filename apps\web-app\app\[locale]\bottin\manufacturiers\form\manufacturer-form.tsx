'use client';

import { ContactDetails } from '@/app/[locale]/bottin/manufacturiers/form/sections/contact-details';
import { Description } from '@/app/[locale]/bottin/manufacturiers/form/sections/description';
import { ResourceForm } from '@/components/resource-form/resource-form';
import {
  type VendorFormSchema,
  getVendorFormSchema,
} from '@/schemas/bottin/vendor-form-schema';
import type { RequestStatus } from '@/types/common';
import type { ManufacturerFormSectionKey } from '@/types/directory/manufacturer';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';

interface ManufacturerFormProps {
  defaultValues?: VendorFormSchema;
  formSections: Record<ManufacturerFormSectionKey, string>;
  handleOnDelete?: () => void;
  onSubmit: (data: VendorFormSchema) => void;
  status: RequestStatus;
};

export const ManufacturerForm = ({
  defaultValues,
  formSections,
  onSubmit,
  status,
}: ManufacturerFormProps) => {
  const t = useTranslations('directory.form.sections');
  const form = useForm<VendorFormSchema>({
    defaultValues,
    mode: 'onBlur',
    resolver: zodResolver(getVendorFormSchema(t)),
    reValidateMode: 'onChange',
  });

  return (
    <ResourceForm
      form={form}
      formSections={formSections}
      onSubmit={onSubmit}
      status={status}
    >
      <Description />
      <ContactDetails />
    </ResourceForm>
  );
}
