import * as fs from 'node:fs/promises';
import { INPUT_PATH, OUTPUT_PATH } from '../constants';
import { logConverterRecordCounts } from '../converter-validator';
import type { Mapping } from '../types';
import { BaseConverter } from './base-converter';

interface MySqlRoom {
  id: number;
  numero: string;
  pseudonyme: string | null;
  superficie: number | null;
  portante_plancher: number | null;
  batiment_id: number;
}

interface PostgresRoom {
  id: string;
  number: string;
  area: number | null;
  floorLoad: number | null;
  building_id: string | null;
}

export class RoomMigrationConverter extends BaseConverter {
  private roomMappings: Mapping[] = [];

  private parseInsertStatement(
    sqlStatement: string,
    sqlContent: string,
  ): MySqlRoom[] {
    const values = this.extractValuesFromInsertStatement(
      sqlStatement,
      sqlContent,
      'local',
    );

    return [
      {
        id: Number.parseInt(values.id),
        numero: values.numero,
        pseudonyme: values.pseudonyme,
        superficie: values.superficie
          ? Number.parseFloat(values.superficie)
          : null,
        portante_plancher: values.portante_plancher
          ? Number.parseFloat(values.portante_plancher)
          : null,
        batiment_id: Number.parseInt(values.batiment_id),
      },
    ];
  }

  private async convertToPostgres(
    mysqlRoom: MySqlRoom,
    buildingMappings: Record<string, string>,
  ): Promise<PostgresRoom> {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.roomMappings.push({
      mysqlId: mysqlRoom.id,
      postgresId: postgresId,
    });

    // Load building ID mappings
    const buildingId =
      buildingMappings[mysqlRoom.batiment_id.toString()] ?? null;

    if (!buildingId) {
      console.warn(
        `No mapping found for building_id: ${mysqlRoom.batiment_id}`,
      );
    }

    return {
      id: postgresId,
      number: mysqlRoom.numero,
      area: mysqlRoom.superficie,
      floorLoad: mysqlRoom.portante_plancher,
      building_id: buildingId,
    };
  }

  async convertFile(): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(INPUT_PATH, 'utf8');

      // Extract INSERT statements for local table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'local',
      );

      if (insertStatements.length === 0) {
        console.log('No local INSERT statements found.');
        return;
      }

      const allPostgresRooms: PostgresRoom[] = [];
      const buildingMappings = await this.loadEntityIdMappings('batiment');

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRooms = this.parseInsertStatement(statement, sqlContent);

        // Process each room record
        for (const mysqlRoom of mysqlRooms) {
          const postgresRoom = await this.convertToPostgres(
            mysqlRoom,
            buildingMappings,
          );
          if (postgresRoom) {
            allPostgresRooms.push(postgresRoom);
          }
        }
      }

      // Add the actual INSERT statement
      const columns = ['id', 'number', 'area', 'floor_load', 'building_id'];

      const postgresInsertsWithMappings =
        this.generatePostgresWithColumnsI18NInsert(
          allPostgresRooms,
          'rooms',
          'Rooms Inserts',
          columns,
        );

      // Create the output directory if it doesn't exist
      const outputDir = OUTPUT_PATH.substring(0, OUTPUT_PATH.lastIndexOf('/'));
      await this.safeMkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await this.safeAppendFile(OUTPUT_PATH, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.roomMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'local', postgres: 'rooms' },
        idMappings,
      );

      console.log('Conversion completed successfully!');

      // Validate the conversion - this will log any record count differences
      await logConverterRecordCounts('RoomMigrationConverter', [
        { mysql: 'local', postgres: 'rooms' },
      ]);
      console.log(`- Found ${allPostgresRooms.length} room records`);
      console.log(`- PostgreSQL inserts written to: ${OUTPUT_PATH}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
