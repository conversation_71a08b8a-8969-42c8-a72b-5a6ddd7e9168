import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import { boolean, pgTable, text, timestamp, unique } from 'drizzle-orm/pg-core';
import { users } from '../auth';
import { addresses } from './addresses.schema';
import { buildings } from './buildings.schema';
import { institutions } from './institutions.schema';
import { locales } from './locales.schema';
import { serviceOffers } from './service-offers.schema';

export const campuses = pgTable(
  'campuses',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    isActive: boolean().notNull().default(true),
    sadId: text(),
    institutionId: text()
      .notNull()
      .references(() => institutions.id, {
        onDelete: 'restrict',
        onUpdate: 'cascade',
      }),
    createdAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp({ withTimezone: true, mode: 'string' })
      .notNull()
      .defaultNow(),
    modifiedBy: text().references(() => users.id),
  },
  (table) => [
    {
      uniqueSadId: unique().on(table.sadId),
    },
  ],
);

export const campusesRelations = relations(campuses, ({ one, many }) => ({
  buildings: many(buildings),
  institution: one(institutions, {
    fields: [campuses.institutionId],
    references: [institutions.id],
  }),
  translations: many(campusesI18N),
  addresses: many(addresses),
  serviceOffers: many(serviceOffers),
}));

export const campusesI18N = pgTable(
  'campuses_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => campuses.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const campusesI18NRelations = relations(campusesI18N, ({ one }) => ({
  campus: one(campuses, {
    fields: [campusesI18N.dataId],
    references: [campuses.id],
  }),
  locale: one(locales, {
    fields: [campusesI18N.locale],
    references: [locales.code],
  }),
}));
