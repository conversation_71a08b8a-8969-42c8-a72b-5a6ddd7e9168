import { DeleteConfirmation } from '@/components/delete-confirmation/delete-confirmation';
import { useDeleteDirectoryEntityHelper } from '@/helpers/use-delete-directory-entity.helpers';
import { extractDirectoryEditPathnames } from '@/i18n/i18n.helpers';
import { pathnames } from '@/i18n/settings';
import { Link } from '@/lib/navigation';
import { Button } from '@/ui/button';
import type { DirectoryEntity } from '@rie/domain/types';
import type { Row } from '@tanstack/react-table';
import { useTranslations } from 'next-intl';
import { FaEdit } from 'react-icons/fa';
import { MdDeleteForever } from 'react-icons/md';

// Base interface that all entity list items must have
interface BaseEntityListItem {
  id: string;
  text?: string;
  name?: string;
}

type DirectoryEntityActionsProps<
  T extends BaseEntityListItem = BaseEntityListItem,
> = {
  row: Row<T>;
  directoryEntity: DirectoryEntity;
};
export const DirectoryEntityActions = <T extends BaseEntityListItem>({
  row,
  directoryEntity,
}: DirectoryEntityActionsProps<T>) => {
  const tCommon = useTranslations('common');

  const { mutate: deleteDirectoryEntity } =
    useDeleteDirectoryEntityHelper(directoryEntity);
  const directoryEditPathnames = extractDirectoryEditPathnames(pathnames);
  const pathname = directoryEditPathnames[directoryEntity];

  // Get the display name from either 'text' or 'name' field
  const displayName = ('text' in row.original && row.original.text) || 
                     ('name' in row.original && row.original.name) || 
                     'Item';

  return (
    <div className="flex items-center justify-center gap-x-3">
      <Button asChild size="icon">
        <Link
          data-testid={`edit-${directoryEntity}`}
          href={{
            params: { id: row.original.id },
            pathname,
          }}
        >
          <FaEdit className="h-4 w-4" />
        </Link>
      </Button>
      <DeleteConfirmation
        onDeleteAction={() => {
          deleteDirectoryEntity(row.original.id);
        }}
        title={tCommon('deleteItemQuestion', { item: displayName })}
        trigger={
          <Button
            size="icon"
            variant="destructive"
            data-testid={`delete-${directoryEntity}`}
          >
            <MdDeleteForever className="h-4 w-4" />
          </Button>
        }
      />
    </div>
  );
};
