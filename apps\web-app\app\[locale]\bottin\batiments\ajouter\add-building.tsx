'use client';

import { BuildingForm } from '@/app/[locale]/bottin/batiments/form/building-form';
import { buildingFormDefaultValues } from '@/constants/directory/building';
import { useCreateBuilding } from '@/hooks/directory/buildings.hook';
import type { BuildingFormSchema } from '@/schemas/bottin/building-form-schema';
import type { SupportedLocale } from '@/types/locale';

type AddBuildingProps = {
  locale: SupportedLocale;
  formSections: Record<string, string>;
};

export const AddBuilding = ({ locale, formSections }: AddBuildingProps) => {
  const { mutate, status } = useCreateBuilding();

  const onSubmit = (data: BuildingFormSchema) => {
    mutate(data);
  };

  return (
    <BuildingForm
      defaultValues={buildingFormDefaultValues(locale)}
      formSections={formSections}
      onSubmit={onSubmit}
      status={status}
    />
  );
};
