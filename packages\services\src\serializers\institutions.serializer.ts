import {
  type InstitutionDetailResponseDTO,
  InstitutionDetailResponseDtoSchema,
  type InstitutionEditResponseDTO,
  InstitutionEditResponseDtoSchema,
  type InstitutionListItemDTO,
  InstitutionListItemDtoSchema,
  type SelectOptionDTO,
  SelectOptionDtoSchema,
} from '@rie/api-contracts';
import type { Institution } from '@rie/domain/aggregates';
import { DbInstitutionDataSchema } from '@rie/domain/schemas';
import type { InstitutionData } from '@rie/domain/types';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';

/**
 * Serializes an Institution aggregate into the shape required for a list view.
 *
 * Uses the Institution aggregate's composite translation logic to provide intelligent
 * locale fallback behavior, ensuring the best available translation is selected
 * for each field based on the requested locale and fallback preferences.
 */
export function serializeInstitutionToListItem(
  institution: Institution,
  locale: string,
  fallbackLocale: string,
): Effect.Effect<InstitutionListItemDTO, ParseResult.ParseError> {
  const transformer = Schema.transformOrFail(
    DbInstitutionDataSchema,
    InstitutionListItemDtoSchema,
    {
      strict: true,
      decode: (institutionData, _options, ast) => {
        return ParseResult.try({
          try: () => {
            // Use the Institution aggregate's composite translation logic for intelligent fallback
            const compositeTranslations = institution.getCompositeTranslations(
              locale,
              fallbackLocale,
            );

            // Ensure we always provide a non-empty string for text
            const nameCandidate = compositeTranslations.name.value;
            const safeText =
              typeof nameCandidate === 'string' && nameCandidate.trim() !== ''
                ? nameCandidate
                : (institutionData.guidId ?? institutionData.id);

            // Get acronym from translations
            const acronymCandidate = compositeTranslations.acronyms.value;
            const safeAcronym =
              typeof acronymCandidate === 'string' && acronymCandidate.trim() !== ''
                ? acronymCandidate
                : null;

            return {
              id: institutionData.id,
              text: safeText,
              acronym: safeAcronym,
              establishmentType: null, // TODO: Implement establishment type lookup
              lastUpdatedAt: institutionData.updatedAt,
              createdAt: institutionData.createdAt,
              uid: institutionData.guidId,
            };
          },
          catch(error) {
            return new ParseResult.Type(
              ast,
              institutionData,
              error instanceof Error
                ? error.message
                : 'Failed to serialize institution to list item',
            );
          },
        });
      },
      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

  return pipe(
    institution.toRaw(),
    Effect.flatMap((raw) => Schema.decode(transformer)(raw)),
    Effect.catchAll((err) =>
      Effect.sync(() => {
        console.error(
          `[Serializer][Institution ListItem] Failed for institution id=${institution.id}:`,
          err,
        );
        throw err;
      }),
    ),
  );
}

/**
 * Serializes an Institution aggregate into the shape required for a detailed view.
 *
 * Uses the Institution aggregate's composite translation logic to provide intelligent
 * locale fallback behavior for all translatable fields. The resulting DTO
 * contains the best available translation for each field based on the requested
 * locale and fallback preferences, formatted for detailed view consumption.
 */
export function serializeInstitutionToDetail(
  institution: Institution,
  locale: string,
  fallbackLocale: string,
): Effect.Effect<InstitutionDetailResponseDTO, ParseResult.ParseError> {
  const transformer = Schema.transformOrFail(
    DbInstitutionDataSchema,
    InstitutionDetailResponseDtoSchema,
    {
      strict: true,
      decode: (institutionData, _options, ast) => {
        return ParseResult.try({
          try: () => {
            // Use the Institution aggregate's composite translation logic for intelligent fallback
            const compositeTranslations = institution.getCompositeTranslations(
              locale,
              fallbackLocale,
            );

            return {
              id: institutionData.id,
              isActive: institutionData.isActive ?? true,
              guidId: institutionData.guidId,
              typeId: institutionData.typeId,
              translations: {
                name: {
                  locale: compositeTranslations.name.locale,
                  value: compositeTranslations.name.value ?? '',
                },
                description: {
                  locale: compositeTranslations.description.locale,
                  value: compositeTranslations.description.value,
                },
                otherNames: {
                  locale: compositeTranslations.otherNames.locale,
                  value: compositeTranslations.otherNames.value,
                },
                acronyms: {
                  locale: compositeTranslations.acronyms.locale,
                  value: compositeTranslations.acronyms.value,
                },
              },
            };
          },
          catch(error) {
            return new ParseResult.Type(
              ast,
              institutionData,
              error instanceof Error
                ? error.message
                : 'Failed to serialize institution to detail',
            );
          },
        });
      },
      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

  return pipe(
    institution.toRaw(),
    Effect.flatMap((raw) => Schema.decode(transformer)(raw)),
    Effect.catchAll((err) =>
      Effect.sync(() => {
        console.error(
          `[Serializer][Institution Detail] Failed for institution id=${institution.id}:`,
          err,
        );
        throw err;
      }),
    ),
  );
}

/**
 * Serializes an Institution aggregate into a simple value/label pair for select inputs.
 *
 * Uses the Institution aggregate's composite translation logic to provide intelligent
 * locale fallback behavior for the label field. This ensures that select options
 * display the best available institution name based on the requested locale and fallback
 * preferences, providing a consistent user experience across different languages.
 */
export function serializeInstitutionToSelectOption(
  institution: Institution,
  locale: string,
  fallbackLocale: string,
): Effect.Effect<SelectOptionDTO, ParseResult.ParseError> {
  const transformer = Schema.transformOrFail(
    DbInstitutionDataSchema,
    SelectOptionDtoSchema,
    {
      strict: true,
      decode: (institutionData, _options, ast) => {
        return ParseResult.try({
          try: () => {
            // Use the Institution aggregate's composite translation logic for intelligent fallback
            const compositeTranslations = institution.getCompositeTranslations(
              locale,
              fallbackLocale,
            );

            // Ensure we always provide a non-empty string for label
            const nameCandidate = compositeTranslations.name.value;
            const safeLabel =
              typeof nameCandidate === 'string' && nameCandidate.trim() !== ''
                ? nameCandidate
                : (institutionData.guidId ?? institutionData.id);

            return {
              value: institutionData.id,
              label: safeLabel,
            };
          },
          catch(error) {
            return new ParseResult.Type(
              ast,
              institutionData,
              error instanceof Error
                ? error.message
                : 'Failed to serialize institution to select option',
            );
          },
        });
      },
      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

  return pipe(
    institution.toRaw(),
    Effect.flatMap((raw) => Schema.decode(transformer)(raw)),
    Effect.catchAll((err) =>
      Effect.sync(() => {
        console.error(
          `[Serializer][Institution SelectOption] Failed for institution id=${institution.id}:`,
          err,
        );
        throw err;
      }),
    ),
  );
}

/**
 * Serializes an Institution aggregate into the shape required for edit forms.
 * This function takes raw institution data and transforms it into an edit-friendly DTO
 * that includes all available translations for form population.
 */
export function serializeInstitutionToFormEdit(
  institutionData: InstitutionData,
): Effect.Effect<InstitutionEditResponseDTO, ParseResult.ParseError> {
  const transformer = Schema.transformOrFail(
    DbInstitutionDataSchema,
    InstitutionEditResponseDtoSchema,
    {
      strict: true,
      decode: (data, _options, ast) => {
        return ParseResult.try({
          try: () => ({
            id: data.id,
            isActive: data.isActive ?? true,
            guidId: data.guidId,
            typeId: data.typeId,
            translations: data.translations.map((t) => ({
              name: { locale: t.locale, value: t.name },
              description: { locale: t.locale, value: t.description },
              otherNames: { locale: t.locale, value: t.otherNames },
              acronyms: { locale: t.locale, value: t.acronyms },
            })),
          }),
          catch(error) {
            return new ParseResult.Type(
              ast,
              data,
              error instanceof Error
                ? error.message
                : 'Failed to serialize institution to form edit',
            );
          },
        });
      },
      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

  return Schema.decode(transformer)(institutionData);
}

/**
 * Serializes an Institution aggregate into the shape required for client edit forms.
 * This includes a jurisdiction label if provided.
 */
export function serializeInstitutionToEditClient(
  institution: Institution,
  establishmentTypeLabel: string | null,
): Effect.Effect<{
  id: string;
  pseudonym: string;
  name: Array<{ locale: string; value: string }>;
}, ParseResult.ParseError> {
  const transformer = Schema.transformOrFail(
    DbInstitutionDataSchema,
    Schema.Struct({
      id: Schema.String,
      pseudonym: Schema.String,
      name: Schema.Array(
        Schema.Struct({
          locale: Schema.String,
          value: Schema.String,
        }),
      ),
    }),
    {
      strict: true,
      decode: (institutionData, _options, ast) => {
        return ParseResult.try({
          try: () => ({
            id: institutionData.id,
            pseudonym: institutionData.guidId ?? '',
            name: institutionData.translations.map((t) => ({
              locale: t.locale,
              value: t.name ?? '',
            })),
          }),
          catch(error) {
            return new ParseResult.Type(
              ast,
              institutionData,
              error instanceof Error
                ? error.message
                : 'Failed to serialize institution to edit client',
            );
          },
        });
      },
      encode: (val, _options, ast) =>
        ParseResult.fail(
          new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
        ),
    },
  );

  return pipe(
    institution.toRaw(),
    Effect.flatMap((raw) => Schema.decode(transformer)(raw)),
    // Convert readonly properties/arrays to mutable shapes expected by the frontend form schema
    Effect.map((dto) => ({
      id: dto.id,
      pseudonym: dto.pseudonym,
      name: dto.name.map((n) => ({ locale: n.locale, value: n.value })),
    })),
    Effect.catchAll((err) =>
      Effect.sync(() => {
        console.error(
          `[Serializer][Institution EditClient] Failed for institution id=${institution.id}:`,
          err,
        );
        throw err;
      }),
    ),
  );
}

/**
 * Transforms client edit form payload into the domain input shape
 * consumed by creation/update flows (no separate mapper file).
 */
export const InstitutionEditClientToDomainInput = Schema.transformOrFail(
  Schema.Struct({
    id: Schema.optional(Schema.String),
    typeId: Schema.NullOr(Schema.String),
    pseudonym: Schema.String,
    name: Schema.Array(
      Schema.Struct({
        locale: Schema.String,
        value: Schema.String,
      }),
    ),
  }),
  Schema.Struct({
    guidId: Schema.NullOr(Schema.String),
    typeId: Schema.String,
    translations: Schema.Array(
      Schema.Struct({
        locale: Schema.String,
        name: Schema.String,
      }),
    ),
  }),
  {
    strict: true,
    decode: (data, _options, ast) =>
      ParseResult.try({
        try: () => ({
          guidId: data.pseudonym ?? null,
          typeId: data.typeId ?? '',
          translations: data.name.map((n) => ({
            locale: n.locale,
            name: n.value,
          })),
        }),
        catch(error) {
          return new ParseResult.Type(
            ast,
            data,
            error instanceof Error
              ? error.message
              : 'Failed to transform institution edit client payload to domain input',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);