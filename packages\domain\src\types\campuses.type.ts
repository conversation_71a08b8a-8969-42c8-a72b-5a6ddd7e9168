import type {
  CampusDetailSchema,
  CampusEditSchema,
  CampusInputSchema,
  CampusListSchema,
  CampusSchema,
  CampusSelectSchema,
  DbCampusDataSchema,
  DbCampusI18NDataSchema,
  DbCampusI18NInputSchema,
} from '../schemas/campuses.schema';

import type * as Schema from 'effect/Schema';

export type Campus = Schema.Schema.Type<typeof CampusSchema>;
export type CampusInput = Schema.Schema.Type<typeof CampusInputSchema>;
export type CampusList = Schema.Schema.Type<typeof CampusListSchema>;
export type CampusSelect = Schema.Schema.Type<typeof CampusSelectSchema>;
export type CampusEdit = Schema.Schema.Type<typeof CampusEditSchema>;
export type CampusDetail = Schema.Schema.Type<typeof CampusDetailSchema>;
export type CampusData = Schema.Schema.Type<typeof DbCampusDataSchema>;
export type CampusTranslationFields = Schema.Schema.Type<
  typeof DbCampusI18NDataSchema
>;
export type CampusI18NInput = Schema.Schema.Type<
  typeof DbCampusI18NInputSchema
>;
