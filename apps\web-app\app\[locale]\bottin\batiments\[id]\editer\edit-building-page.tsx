'use client';
import { BuildingForm } from '@/app/[locale]/bottin/batiments/form/building-form';
import { LoadingResource } from '@/components/loading-resource/loading-resource';
import { useUpdateBuilding } from '@/hooks/directory/buildings.hook';
import { useGetDirectoryById } from '@/hooks/directory/directory-list.hook';
import type { BuildingFormSchema } from '@/schemas/bottin/building-form-schema';
import type { BuildingFormSectionKey } from '@/types/building';
import type { SupportedLocale } from '@/types/locale';

type EditBuildingPageParams = {
  formSections: Record<BuildingFormSectionKey, string>;
  id: string;
  locale: SupportedLocale;
};

export default function EditionBuildingPage({
  formSections,
  id,
}: EditBuildingPageParams) {
  const {
    data: building,
    error,
    isPending,
  } = useGetDirectoryById<'building', 'edit'>({
    directoryListKey: 'building',
    id,
    view: 'edit',
  });

  if (isPending) {
    return <LoadingResource />;
  }

  if (error) {
    return <div className="p-4 text-red-500"> Erreur : {error.message}</div>;
  }

  const { mutate, status } = useUpdateBuilding();
  const onSubmit = (data: BuildingFormSchema) => {
    mutate({ id, payload: data });
  };

  return (
    <BuildingForm
      defaultValues={building}
      formSections={formSections}
      onSubmit={onSubmit}
      status={status}
    />
  );
}
