import * as Data from 'effect/Data';

export class InstitutionNotFoundError extends Data.TaggedError(
  'InstitutionNotFoundError',
)<{ readonly id: string }> { }

export class InstitutionValidationError extends Data.TaggedError(
  'InstitutionValidationError',
)<{
  readonly message: string;
  readonly fields?: Record<string, string>;
}> { }

export class InstitutionInvariantViolationError extends Data.TaggedError(
  'InstitutionInvariantViolationError',
)<{
  readonly institutionId: string;
  readonly reason: string;
}> { }

export class InstitutionPersistenceError extends Data.TaggedError(
  'InstitutionPersistenceError',
)<{
  readonly message: string;
}> { }
