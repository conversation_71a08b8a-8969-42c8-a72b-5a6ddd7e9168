import { BaseConverter } from './base-converter';

export class UnitParentUpdater extends BaseConverter {
  async updateUnitParents(
    INPUT_PATH: string,
    OUTPUT_PATH: string,
  ): Promise<void> {
    try {
      const unitParentIdMappings =
        await this.loadEntityIdMappings('juridiction');

      const postgresUnitUpdates = Object.entries(unitParentIdMappings).map(
        ([unitId, value]) => ({
          unitId,
          uniteParentId: value.unit_parent_id,
        }),
      );

      // Create SQL updates
      let updateStatements = this.generateCommentHeader(
        'Update Unit Parent References',
      );

      for (const update of postgresUnitUpdates) {
        updateStatements += `UPDATE "units" SET "parent_id" = '${update.uniteParentId}' WHERE "id" = '${update.unitId}';\n`;
      }

      // Create the output directory if it doesn't exist
      const outputDir = OUTPUT_PATH.substring(0, OUTPUT_PATH.lastIndexOf('/'));
      await this.safeMkdir(outputDir, { recursive: true });

      // Write the update statements to file
      await this.safeAppendFile(OUTPUT_PATH, updateStatements);

      console.log('Unit parent references updated successfully!');
      console.log(
        `- Generated ${postgresUnitUpdates.length} unit parent updates`,
      );
    } catch (error) {
      console.error('Error updating unit parent references:', error);
      throw error;
    }
  }
}
