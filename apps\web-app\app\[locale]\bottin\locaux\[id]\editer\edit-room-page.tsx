'use client';
import { RoomForm } from '@/app/[locale]/bottin/locaux/form/room-form';
import { LoadingResource } from '@/components/loading-resource/loading-resource';
import { useGetDirectoryById } from '@/hooks/directory/directory-list.hook';
import { useUpdateRoom } from '@/hooks/directory/rooms.hook';
import type { RoomFormSchema } from '@/schemas/bottin/room-form-schema';
import type { RoomFormSectionKey } from '@/types/directory/room';
import type { SupportedLocale } from '@/types/locale';

type EditRoomProps = {
  formSections: Record<RoomFormSectionKey, string>;
  id: string;
  locale: SupportedLocale;
};

export const EditionRoomPage = ({ formSections, id }: EditRoomProps) => {
  const {
    data: room,
    error,
    isPending,
  } = useGetDirectoryById<'local', 'edit'>({
    directoryListKey: 'local',
    id,
    view: 'edit',
  });

  if (isPending) {
    return <LoadingResource />;
  }

  if (error) {
    return <div className="p-4 text-red-500">Erreur : {error.message}</div>;
  }

  const { mutate, status } = useUpdateRoom();
  const onSubmit = (data: RoomFormSchema) => {
    mutate({ id, payload: data });
  };

  return (
    <RoomForm
      defaultValues={room}
      formSections={formSections}
      onSubmit={onSubmit}
      status={status}
    />
  );
};
