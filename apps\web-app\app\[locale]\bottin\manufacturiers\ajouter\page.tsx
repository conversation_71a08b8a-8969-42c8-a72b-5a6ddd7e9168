import { AddManufacturer } from '@/app/[locale]/bottin/manufacturiers/ajouter/add-manufacturer';
import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { manufacturerFormSections } from '@/constants/directory/manufacturer';
import { getQueryClientOptions } from '@/constants/query-client';
import { getDirectoryListOptions } from '@/hooks/directory/directory-list.options';
import { redirect } from '@/lib/navigation';
import type { BasePageParams, DirectoryListKey } from '@/types/common';
import { auth } from '@rie/auth';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import { headers } from 'next/headers';

export default async function NewManufacturerPage(props: BasePageParams) {
  const params = await props.params;

  const { locale } = params;
  const sessionData = await auth.api.getSession({ headers: await headers() });
  
    if (!sessionData?.user) {
      return redirect({
        href: {
          pathname: '/login',
          query: { from: '/bottin/manufacturiers/ajouter' },
        },
        locale,
      });
    }

  const formSections = await getFormSections({
    resourceName: 'manufacturers',
    sections: manufacturerFormSections,
  });

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const directoryLists: DirectoryListKey[] = ['local', 'building'];

  const t0 = performance.now();

  // Prefetch toutes les listes contrôlées en une seule requête (plus efficace)
  await Promise.all(
    directoryLists.map((directoryListKey) =>
      queryClient.prefetchQuery(
        getDirectoryListOptions({
          directoryListKey,
          locale,
          view: 'select',
        }),
      ),
    ),
  );

  const t1 = performance.now();

  console.log(
    `Call to fetch controlledLists "local" and "building" took ${t1 - t0} milliseconds.`,
  );

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <AddManufacturer locale={locale} formSections={formSections} />
    </HydrationBoundary>
  );
}
