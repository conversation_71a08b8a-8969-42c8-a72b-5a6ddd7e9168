'use client';

import { DirectoryEntityActions } from '@/app/[locale]/bottin/directory-page/directory-entity-actions';
import { HeaderRenderer } from '@/components/header-renderer/header-renderer';
import { PermissionGate } from '@/components/permissions/permission-gate';
import { Button } from '@/components/ui/button';
import { extractDirectoryEditPathnames } from '@/i18n/i18n.helpers';
import { pathnames } from '@/i18n/settings';
import { Link } from '@/lib/navigation';
import type { Entity } from '@/types/directory/directory';
import { DirectoryEntity } from '@rie/domain/types';
import type { ColumnDef } from '@tanstack/react-table';
import { useFormatter } from 'next-intl';
import { FaEdit } from 'react-icons/fa';

const directoryEditPathnames = extractDirectoryEditPathnames(pathnames);

export const selectEntityColumns = (
  directoryEntity: DirectoryEntity,
): ColumnDef<Entity>[] => {
  const columnMap: Record<DirectoryEntity, () => ColumnDef<Entity>[]> = {
    building: buildingsColumns,
    campus: campusColumns,
    institution: establishmentsColumns,
    fundingProject: financingProjectsColumns,
    vendor: manufacturersColumns,
    people: peopleColumns,
    room: roomsColumns,
    unit: unitsColumns,
  };

  const columnFunction = columnMap[directoryEntity];
  return columnFunction();
};

const commonColumnsStart = (
  directoryEntity: DirectoryEntity,
): ColumnDef<Entity>[] => [
  {
    accessorKey:
      directoryEntity === 'building'
        ? 'nom'
        : directoryEntity === 'room'
          ? 'numero'
          : directoryEntity === 'people'
            ? 'lastName'
            : directoryEntity === 'vendor' || directoryEntity === 'unit'
              ? 'name'
              : 'text',
    cell: (cell) => cell.getValue(),
    enableHiding: false,
    enablePinning: true,
    header: ({ column }) => (
      <HeaderRenderer
        namespace={`directory.${directoryEntity}`}
        translationKey={`table.columns.${column.id}`}
      />
    ),
    id: 'name',
    maxSize: 400,
    minSize: 250,
  },
];
const commonColumnsEnd = (
  directoryEntity: DirectoryEntity,
): ColumnDef<Entity>[] => [
  {
    accessorKey: 'lastUpdatedAt',
    cell: ({ cell }) => {
      const format = useFormatter();
      const date = new Date(cell.getValue() as string);
      const formattedDate = cell.getValue()
        ? format.dateTime(date, {
            day: 'numeric',
            hour: '2-digit',
            hour12: false,
            minute: '2-digit',
            month: 'long',
            second: '2-digit',
            weekday: 'long',
            year: 'numeric',
          })
        : '-';

      return (
        <div className="flex items-center">
          <span>{formattedDate}</span>
        </div>
      );
    },
    enableHiding: true,
    enablePinning: false,
    header: ({ column }) => (
      <HeaderRenderer
        namespace={`directory.${directoryEntity}`}
        translationKey={`table.columns.${column.id}`}
      />
    ),
    maxSize: 350,
    minSize: 200,
  },
  {
    accessorKey: 'actions',
    enableHiding: false,
    enablePinning: false,
    header: () => (
      <HeaderRenderer
        namespace={`directory.${directoryEntity}`}
        translationKey="table.columns.actions"
      />
    ),
    cell: ({ row }) => (
      <DirectoryEntityActions row={row} directoryEntity={directoryEntity} />
    ),
    size: 105,
  },
];

export const manufacturersColumns = (): ColumnDef<Entity>[] => {
  const directoryEntity = 'vendor' as const;
  return [
    ...commonColumnsStart(directoryEntity),
    {
      accessorKey: 'dateEnd',
      cell: (cell) => {
        const format = useFormatter();
        cell.getValue()
          ? format.dateTime(new Date(cell.getValue() as string), {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })
          : '-';
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey={`table.columns.${column.id}`}
        />
      ),
      maxSize: 350,
      minSize: 200,
    },
    ...commonColumnsEnd(directoryEntity),
  ];
};

export const establishmentsColumns = (): ColumnDef<Entity>[] => {
  const directoryEntity = 'institution' as const;
  return [
    ...commonColumnsStart(directoryEntity),
    {
      accessorKey: 'acronym',
      cell: (cell) => cell.getValue(),
      enableHiding: true,
      enablePinning: false,
      header: ({ column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey={`table.columns.${column.id}`}
        />
      ),
      maxSize: 100,
    },
    {
      accessorKey: 'establishmentType',
      cell: (cell) => cell.getValue(),
      enableHiding: true,
      enablePinning: false,
      header: ({ column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey={`table.columns.${column.id}`}
        />
      ),
      maxSize: 150,
    },
    ...commonColumnsEnd(directoryEntity),
  ];
};

export const unitsColumns = (): ColumnDef<Entity>[] => {
  const directoryEntity = 'unit' as const;
  return [
    ...commonColumnsStart(directoryEntity),
    {
      accessorKey: 'acronym',
      cell: (cell) => cell.getValue(),
      enableHiding: true,
      enablePinning: false,
      header: ({ column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey={`table.columns.${column.id}`}
        />
      ),
      maxSize: 100,
    },
    {
      accessorKey: 'parentName',
      cell: (cell) => cell.getValue(),
      enableHiding: true,
      enablePinning: false,
      header: ({ column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey={`table.columns.${column.id}`}
        />
      ),
      maxSize: 100,
    },
    ...commonColumnsEnd(directoryEntity),
  ];
};

export const peopleColumns = (): ColumnDef<Entity>[] => {
  const directoryEntity = 'people' as const;
  return [
    ...commonColumnsStart(directoryEntity),
    {
      accessorKey: 'firstName',
      cell: (cell) => cell.getValue(),
      enableHiding: true,
      enablePinning: false,
      header: ({ column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey={`table.columns.${column.id}`}
        />
      ),
      maxSize: 100,
    },
    {
      accessorKey: 'email',
      cell: (cell) => {
        const value = cell.getValue();
        const emails = Array.isArray(value) ? value : [];

        return (
          <>
            {emails.map((email) => (
              <span
                className="block"
                key={
                  typeof email === 'object' && email !== null && 'text' in email
                    ? email.text
                    : String(email)
                }
              >
                {typeof email === 'object' && email !== null && 'text' in email
                  ? email.text
                  : String(email)}
              </span>
            ))}
          </>
        );
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey={`table.columns.${column.id}`}
        />
      ),
      maxSize: 150,
    },
    ...commonColumnsEnd(directoryEntity),
  ];
};

export const roomsColumns = (): ColumnDef<Entity>[] => {
  const directoryEntity = 'room' as const;
  return [
    ...commonColumnsStart(directoryEntity),
    {
      accessorKey: 'building',
      cell: (cell) => cell.getValue(),
      enableHiding: true,
      enablePinning: false,
      header: ({ column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey={`table.columns.${column.id}`}
        />
      ),
      maxSize: 150,
    },
    {
      accessorKey: 'jurisdiction',
      cell: ({ cell, row }) => {
        const jurisdiction = cell.getValue() as string;
        const pathname = directoryEditPathnames[directoryEntity];

        return (
          <div className="flex items-center justify-between">
            <span>{jurisdiction}</span>
            <Link
              href={{
                params: { id: row.original.id },
                pathname,
              }}
            >
              <Button size="icon" variant="ghost">
                <FaEdit className="h-4 w-4" />
              </Button>
            </Link>
          </div>
        );
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey={`table.columns.${column.id}`}
        />
      ),
      maxSize: 150,
    },
  ];
};

export const buildingsColumns = (): ColumnDef<Entity>[] => {
  const directoryEntity = 'building' as const;
  return [
    ...commonColumnsStart(directoryEntity),
    {
      accessorKey: 'campus',
      cell: (cell) => cell.getValue(),
      enableHiding: true,
      enablePinning: false,
      header: ({ column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey={`table.columns.${column.id}`}
        />
      ),
      maxSize: 150,
    },
    {
      accessorKey: 'jurisdiction',
      cell: ({ cell, row }) => {
        const jurisdiction = cell.getValue() as string;
        const pathname = directoryEditPathnames[directoryEntity];

        return (
          <div className="flex items-center justify-between">
            <span>{jurisdiction}</span>
            <PermissionGate action="update" resourceType="building">
              <Link
                href={{
                  params: { id: row.original.id },
                  pathname,
                }}
              >
                <Button size="icon" variant="ghost">
                  <FaEdit className="h-4 w-4" />
                </Button>
              </Link>
            </PermissionGate>
          </div>
        );
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey={`table.columns.${column.id}`}
        />
      ),
      maxSize: 150,
    },
  ];
};

export const campusColumns = (): ColumnDef<Entity>[] => {
  const directoryEntity = 'campus' as const;
  return [
    ...commonColumnsStart(directoryEntity),
    {
      accessorKey: 'jurisdiction',
      cell: ({ cell, row }) => {
        const jurisdiction = cell.getValue() as string;
        const pathname = directoryEditPathnames[directoryEntity];

        return (
          <div className="flex items-center justify-between">
            <span>{jurisdiction}</span>
            <Link
              href={{
                params: { id: row.original.id },
                pathname,
              }}
            >
              <Button size="icon" variant="ghost">
                <FaEdit className="h-4 w-4" />
              </Button>
            </Link>
          </div>
        );
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey={`table.columns.${column.id}`}
        />
      ),
      maxSize: 150,
    },
  ];
};

export const financingProjectsColumns = (): ColumnDef<Entity>[] => {
  const directoryEntity = 'fundingProject' as const;
  return [
    ...commonColumnsStart(directoryEntity),
    {
      accessorKey: 'titulaire',
      cell: (cell) => cell.getValue(),
      enableHiding: true,
      enablePinning: false,
      header: ({ column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey={`table.columns.${column.id}`}
        />
      ),
      maxSize: 150,
    },
    {
      accessorKey: 'infrastructure',
      cell: (cell) => cell.getValue(),
      enableHiding: true,
      enablePinning: false,
      header: ({ column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey={`table.columns.${column.id}`}
        />
      ),
      maxSize: 150,
    },
    ...commonColumnsEnd(directoryEntity),
  ];
};
