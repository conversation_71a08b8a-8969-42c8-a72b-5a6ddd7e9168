'use client';

import { FinancingProjectForm } from '@/app/[locale]/bottin/projets-financement/form/financing-project-form';
import { projectFormDefaultValues } from '@/constants/directory/financing-project';
import { useCreateFundingProject } from '@/hooks/directory/funding-projects.hook';
import type { FinancingProjectFormSchema } from '@/schemas/bottin/financing-project-form-schema';
import type { SupportedLocale } from '@/types/locale';

type AddFinancingProjectProps = {
  locale: SupportedLocale;
  formSections: Record<string, string>;
};

export const AddFinancingProject = ({
  locale,
  formSections,
}: AddFinancingProjectProps) => {
  const { mutate, status } = useCreateFundingProject();
  const onSubmit = (data: FinancingProjectFormSchema) => {
    mutate(data);
  };

  return (
    <FinancingProjectForm
      defaultValues={projectFormDefaultValues(locale)}
      formSections={formSections}
      onSubmit={onSubmit}
      status={status}
    />
  );
};
