'use client';

import { Description } from '@/app/[locale]/bottin/unites/form/sections/description';
import { Affiliations } from '@/app/[locale]/infrastructures/form/sections/affiliations/affiliations';
import { ResourceForm } from '@/components/resource-form/resource-form';
import {
  type UnitFormSchema,
  getUnitFormSchema,
} from '@/schemas/bottin/unit-form-schema';
import type { RequestStatus } from '@/types/common';
import type { UnitFormSectionKey } from '@/types/directory/unit';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';

interface UnitsFormProps {
  defaultValues?: UnitFormSchema;
  formSections: Record<UnitFormSectionKey, string>;
  handleOnDelete?: () => void;
  onSubmit: (data: UnitFormSchema) => void;
  status: RequestStatus;
};

export const UnitForm = ({
  defaultValues,
  formSections,
  onSubmit,
  status,
}: UnitsFormProps) => {
  const t = useTranslations(
    'units.form.sections.description.generalInfo.fields',
  );
  const form = useForm<UnitFormSchema>({
    defaultValues,
    mode: 'onBlur',
    resolver: zodResolver(getUnitFormSchema(t)),
    reValidateMode: 'onChange',
  });

  return (
    <ResourceForm
      form={form}
      formSections={formSections}
      onSubmit={onSubmit}
      status={status}
    >
      <Description />
      <Affiliations />
    </ResourceForm>
  );
}
