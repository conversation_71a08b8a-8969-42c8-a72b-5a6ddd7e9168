import * as Data from 'effect/Data';

export class RoomNotFoundError extends Data.TaggedError('RoomNotFoundError')<{
  readonly id: string;
}> { }

export class RoomValidationError extends Data.TaggedError(
  'RoomValidationError',
)<{
  readonly message: string;
  readonly fields?: Record<string, string>;
}> { }

export class RoomInvariantViolationError extends Data.TaggedError(
  'RoomInvariantViolationError',
)<{
  readonly roomId: string;
  readonly reason: string;
}> { }
