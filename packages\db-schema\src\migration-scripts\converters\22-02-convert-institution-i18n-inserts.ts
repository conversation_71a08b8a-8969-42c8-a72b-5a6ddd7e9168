import * as fs from 'node:fs/promises';
import { INPUT_PATH, OUTPUT_PATH } from '../constants';
import type { Mapping } from '../types';
import { BaseConverter } from './base-converter';

interface MySQLOrganizationI18N {
  id: number;
  data_id: number;
  locale: string;
  name: string | null;
  otherNames: string | null;
  acronym: string | null;
  description: string | null;
}

interface PostgresInstitutionI18N {
  id: string;
  data_id: string;
  locale: string;
  name: string | null;
  description: string | null;
  otherNames: string | null;
  acronym: string | null;
}

export class OrganizationI18nMigrationConverter extends BaseConverter {
  private organizationI18nMappings: Mapping[] = [];

  private parseInstitutionI18NInsertStatement(
    sqlStatement: string,
    sqlContent: string,
  ): MySQLOrganizationI18N[] {
    const values = this.extractValuesFromInsertStatement(
      sqlStatement,
      sqlContent,
      'organization_trad',
    );

    return [
      {
        id: Number.parseInt(values.id),
        data_id: Number.parseInt(values.data_id),
        locale: values.language_id,
        name: values.name,
        otherNames: values.pseudonyme,
        acronym: values.acronym,
        description: values.description,
      },
    ];
  }

  private convertToPostgres(
    mysqlRecord: MySQLOrganizationI18N,
    etablissementIdMappings: Record<string, string>,
    validOrganizationIds: Set<number>,
  ): PostgresInstitutionI18N | null {
    // Skip if the data_id is not in our valid list (not an 'etablissement')
    if (!validOrganizationIds.has(mysqlRecord.data_id)) {
      return null;
    }

    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the institution
    const newInstitutionId =
      etablissementIdMappings[mysqlRecord.data_id.toString()];
    if (!newInstitutionId) {
      console.warn(
        `No mapping found for institution_id: ${mysqlRecord.data_id}`,
      );
      return null;
    }

    // Store mapping for future reference
    this.organizationI18nMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      data_id: newInstitutionId,
      locale: mysqlRecord.locale,
      name: mysqlRecord.name,
      description: mysqlRecord.description,
      otherNames: mysqlRecord.otherNames,
      acronym: mysqlRecord.acronym,
    };
  }

  async convertFile(): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(INPUT_PATH, 'utf8');

      // Extract INSERT statements for organization_trad table (old MySQL table name)
      const orgTranslationsStatements = this.extractInsertStatements(
        sqlContent,
        'organization_trad',
      );

      if (orgTranslationsStatements.length === 0) {
        console.log('No organization_trad INSERT statements found.');
        return;
      }

      // Load organization ID mappings
      const etablissementIdMappings =
        await this.loadEntityIdMappings('etablissement');

      // Get the list of valid organization IDs (those with table_ref='etablissement')
      const validEtablissementIds = new Set<number>();

      // Read the original SQL file again to extract organization records
      const orgInsertStatements = this.extractInsertStatements(
        sqlContent,
        'organization',
      );

      for (const statement of orgInsertStatements) {
        const values = this.extractValuesFromInsertStatement(
          statement,
          sqlContent,
          'organization',
        );
        // console.log('values', values);
        if (values.table_ref === 'etablissement') {
          validEtablissementIds.add(Number.parseInt(values.organization_id));
        }
      }

      const allPostgresRecords: PostgresInstitutionI18N[] = [];

      // Process each INSERT statement
      for (const statement of orgTranslationsStatements) {
        const mysqlRecords = this.parseInstitutionI18NInsertStatement(
          statement,
          sqlContent,
        );

        for (const record of mysqlRecords) {
          const postgresRecord = this.convertToPostgres(
            record,
            etablissementIdMappings,
            validEtablissementIds,
          );

          if (postgresRecord) {
            allPostgresRecords.push(postgresRecord);
          }
        }
      }

      // Generate output
      const postgresInserts = this.generatePostgresWithColumnsI18NInsert(
        allPostgresRecords,
        'institutions_i18n',
        'Institution I18n Inserts',
        [
          'id',
          'data_id',
          'locale',
          'name',
          'description',
          'other_names',
          'acronyms',
        ],
      );

      // Create the output directory if it doesn't exist
      const outputDir = OUTPUT_PATH.substring(0, OUTPUT_PATH.lastIndexOf('/'));
      await this.safeMkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await this.safeAppendFile(OUTPUT_PATH, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.organizationI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'etablissement_trad', postgres: 'institutions_i18n' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRecords.length} institutions_i18n records (filtered by valid organization IDs)`,
      );
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
