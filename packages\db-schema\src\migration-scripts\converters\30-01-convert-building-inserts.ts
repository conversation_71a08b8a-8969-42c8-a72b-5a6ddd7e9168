import * as fs from 'node:fs/promises';
import { INPUT_PATH, OUTPUT_PATH } from '../constants';
import { logConverterRecordCounts } from '../converter-validator';
import type { Mapping, MySQLBuilding, PostgresBuilding } from '../types';
import { BaseConverter } from './base-converter';

export class BuildingMigrationConverter extends BaseConverter {
  private buildingMappings: Mapping[] = [];

  private parseInsertStatement(
    sqlStatement: string,
    sqlContent: string,
  ): MySQLBuilding[] {
    const values = this.extractValuesFromInsertStatement(
      sqlStatement,
      sqlContent,
      'batiment',
    );

    return [
      {
        id: Number.parseInt(values.id),
        sad_id: values.uid,
        campus_id:
          values.campus_id !== 'NULL'
            ? Number.parseInt(values.campus_id)
            : null,
      },
    ];
  }

  private async convertToPostgres(
    mysqlBuilding: MySQLBuilding,
    campusIdMappings: Record<string, string>,
  ): Promise<PostgresBuilding> {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.buildingMappings.push({
      mysqlId: mysqlBuilding.id,
      postgresId: postgresId,
    });

    // Map the campus ID
    let campusId = null;
    if (mysqlBuilding.campus_id !== null) {
      campusId = campusIdMappings[mysqlBuilding.campus_id.toString()] ?? null;
      if (!campusId && mysqlBuilding.campus_id) {
        console.warn(
          `No mapping found for campus_id: ${mysqlBuilding.campus_id}`,
        );
      }
    }

    return {
      id: postgresId,
      campus_id: campusId,
      civic_address_id: null,
      sad_id: mysqlBuilding.sad_id,
      di_id: null,
    };
  }

  async convertFile(): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(INPUT_PATH, 'utf8');

      // Extract INSERT statements for batiment table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'batiment',
      );

      if (insertStatements.length === 0) {
        console.log('No batiment INSERT statements found.');
        return;
      }

      const campusIdMappings = await this.loadEntityIdMappings('campus');

      const allPostgresBuildings: PostgresBuilding[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlBuildings = this.parseInsertStatement(statement, sqlContent);
        const postgresBuildings = await Promise.all(
          mysqlBuildings.map((building) =>
            this.convertToPostgres(building, campusIdMappings),
          ),
        );
        allPostgresBuildings.push(...postgresBuildings);
      }

      const columns = [
        'id',
        'campus_id',
        'civic_address_id',
        'sad_id',
        'di_id',
      ];
      const postgresInsertsWithMappings =
        this.generatePostgresWithColumnsI18NInsert(
          allPostgresBuildings,
          'buildings',
          'Building Inserts',
          columns,
        );

      // Create the output directory if it doesn't exist
      const outputDir = OUTPUT_PATH.substring(0, OUTPUT_PATH.lastIndexOf('/'));
      await this.safeMkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await this.safeAppendFile(OUTPUT_PATH, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.buildingMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'batiment', postgres: 'buildings' },
        idMappings,
      );

      console.log('Conversion completed successfully!');

      // Validate the conversion - this will log any record count differences
      await logConverterRecordCounts('BuildingMigrationConverter', [
        { mysql: 'batiment', postgres: 'buildings' },
      ]);
      console.log(`- Found ${allPostgresBuildings.length} buildings records`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
