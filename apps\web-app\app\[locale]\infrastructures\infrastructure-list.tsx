'use client';
import { infrastructuresColumns } from '@/app/[locale]/infrastructures/columns';
import { ResourceList } from '@/components/list-resource/list-resource';
import { initialColumnVisibility } from '@/constants/infrastructures';
import { useGetInfrastructuresList } from '@/hooks/infrastructure/useGetInfrastructuresList';
import { mapInfrastructuresToTableColumns } from '@/services/mappers/infrastructures';
import type { SupportedLocale } from '@/types/locale';
import { useSearchParams } from 'next/navigation';
import { useQueryState } from 'nuqs';
import { useMemo, useRef, useState } from 'react';

type InfrastructureListProps = {
  locale: SupportedLocale;
};

export const InfrastructureList = ({ locale }: InfrastructureListProps) => {
  const [{ pageIndex, pageSize }, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  // Read search params directly from URL like useGetEquipmentsList
  const [searchQuery] = useQueryState('q');
  const [sortValue] = useQueryState('sort');
  const searchParams = useSearchParams();

  // Create combined query parameters
  const combinedQueryParams = useMemo(() => {
    const allParams = new URLSearchParams();

    // Add search query if it exists
    if (searchQuery) {
      allParams.append('q', searchQuery);
    }

    // Add sort value if it exists
    if (sortValue) {
      allParams.append('sort', sortValue);
    }

    // Add all filter parameters from the URL
    for (const [key, value] of Array.from(searchParams.entries())) {
      if (key.startsWith('ff[')) {
        allParams.append(key, value);
      }
    }

    return allParams.toString();
  }, [searchQuery, sortValue, searchParams]);

  // Track previous query params to detect changes
  const prevQueryParamsRef = useRef(combinedQueryParams);

  // Reset pagination when query params change
  if (prevQueryParamsRef.current !== combinedQueryParams) {
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
    prevQueryParamsRef.current = combinedQueryParams;
  }

  const { data: infrastructuresList, isLoading } = useGetInfrastructuresList({
    pageParam: pageIndex * pageSize,
    params: {
      lang: locale,
      limit: pageSize.toString(),
    },
    queryParams: combinedQueryParams,
    select: mapInfrastructuresToTableColumns,
  });

  const infrastructures = infrastructuresList?.data ?? [];
  const totalCount = infrastructuresList?.count ?? 0;

  return (
    <ResourceList
      columns={infrastructuresColumns(locale)}
      data={infrastructures ?? []}
      initialColumnVisibility={initialColumnVisibility}
      isLoading={isLoading}
      onPageChange={setPagination}
      pageIndex={pageIndex}
      pageSize={pageSize}
      resourceName="infrastructures"
      totalCount={totalCount}
    />
  );
};
