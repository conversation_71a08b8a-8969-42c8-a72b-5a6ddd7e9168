import { DBSchema } from '@rie/db-schema';
import type { DBBuildingInput } from '@rie/domain/types';
import { PgDatabaseLive } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';

export class BuildingsRepositoryLive extends Effect.Service<BuildingsRepositoryLive>()(
  'BuildingsRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const dbClient = yield* PgDatabaseLive;

      const findAllBuildings = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.buildings.findMany({
            columns: {
              id: true,
              campusId: true,
              civicAddressId: true,
              sadId: true,
              diId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                },
              },
              campus: {
                columns: {
                  id: true,
                },
                with: {
                  translations: {
                    columns: {
                      locale: true,
                      name: true,
                    },
                  },
                },
              },
            },
          }),
        );
      });

      const findBuildingById = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client.query.buildings.findFirst({
            where: eq(DBSchema.buildings.id, id),
            columns: {
              id: true,
              campusId: true,
              civicAddressId: true,
              sadId: true,
              diId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              translations: {
                columns: {
                  id: true,
                  locale: true,
                  name: true,
                  description: true,
                  otherNames: true,
                },
              },
              campus: {
                columns: {
                  id: true,
                },
                with: {
                  translations: {
                    columns: {
                      locale: true,
                      name: true,
                    },
                  },
                },
              },
            },
          }),
        );
      });

      const createBuilding = ({
        building,
      }: {
        building: DBBuildingInput;
      }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            const { translations, ...buildingData } = building;

            // Create the building
            const [createdBuilding] = yield* tx((client) =>
              client.insert(DBSchema.buildings).values(buildingData).returning({
                id: DBSchema.buildings.id,
                campusId: DBSchema.buildings.campusId,
                civicAddressId: DBSchema.buildings.civicAddressId,
                sadId: DBSchema.buildings.sadId,
                diId: DBSchema.buildings.diId,
                createdAt: DBSchema.buildings.createdAt,
                updatedAt: DBSchema.buildings.updatedAt,
                modifiedBy: DBSchema.buildings.modifiedBy,
              }),
            );

            if (!createdBuilding) {
              return yield* Effect.fail(new Error('Failed to create building'));
            }

            // Create the translations
            const translationsToInsert = translations.map(
              (translation) => ({
                dataId: createdBuilding.id,
                locale: translation.locale,
                name: translation.name,
                description: translation.description ?? null,
                otherNames: translation.otherNames ?? null,
                acronyms: null, // Not used yet
              }),
            );
            const createdTranslations = yield* tx((client) =>
              client
                .insert(DBSchema.buildingsI18N)
                .values(translationsToInsert)
                .returning({
                  id: DBSchema.buildingsI18N.id,
                  locale: DBSchema.buildingsI18N.locale,
                  name: DBSchema.buildingsI18N.name,
                  description: DBSchema.buildingsI18N.description,
                  otherNames: DBSchema.buildingsI18N.otherNames,
                }),
            );

            return {
              ...createdBuilding,
              translations: createdTranslations,
            };
          });
        });
      };

      const updateBuilding = ({
        buildingId,
        building,
      }: {
        buildingId: string;
        building: DBBuildingInput;
      }) => {
        return dbClient.transaction((tx) => {
          return Effect.gen(function* () {
            // Update the building
            const { translations, ...buildingData } = building;
            const [updatedBuilding] = yield* tx((client) =>
              client
                .update(DBSchema.buildings)
                .set(buildingData)
                .where(eq(DBSchema.buildings.id, buildingId))
                .returning({
                  id: DBSchema.buildings.id,
                  campusId: DBSchema.buildings.campusId,
                  civicAddressId: DBSchema.buildings.civicAddressId,
                  sadId: DBSchema.buildings.sadId,
                  diId: DBSchema.buildings.diId,
                  createdAt: DBSchema.buildings.createdAt,
                  updatedAt: DBSchema.buildings.updatedAt,
                  modifiedBy: DBSchema.buildings.modifiedBy,
                }),
            );

            // Delete existing translations
            yield* tx((client) =>
              client
                .delete(DBSchema.buildingsI18N)
                .where(eq(DBSchema.buildingsI18N.dataId, buildingId)),
            );

            // Insert new translations
            const translationsToInsert = translations.map(
              (translation) => ({
                dataId: buildingId,
                locale: translation.locale,
                name: translation.name,
                description: translation.description ?? null,
                otherNames: translation.otherNames ?? null,
                acronyms: null, // Not used yet
              }),
            );

            const updatedTranslations = yield* tx((client) =>
              client
                .insert(DBSchema.buildingsI18N)
                .values(translationsToInsert)
                .returning({
                  id: DBSchema.buildingsI18N.id,
                  locale: DBSchema.buildingsI18N.locale,
                  name: DBSchema.buildingsI18N.name,
                  description: DBSchema.buildingsI18N.description,
                  otherNames: DBSchema.buildingsI18N.otherNames,
                }),
            );

            // Return the building with its translations directly
            return {
              ...updatedBuilding,
              translations: updatedTranslations,
            };
          });
        });
      };

      const deleteBuilding = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client
            .delete(DBSchema.buildings)
            .where(eq(DBSchema.buildings.id, id))
            .returning({ id: DBSchema.buildings.id }),
        );
      });

      return {
        findAllBuildings,
        findBuildingById,
        createBuilding,
        updateBuilding,
        deleteBuilding,
      } as const;
    }),
  },
) { }
