{"name": "@rie/domain", "version": "1.0.0", "private": true, "type": "module", "module": "./build/index.js", "types": "./build/dts/index.d.ts", "main": "./build/index.js", "exports": {".": {"import": "./build/index.js", "require": "./build/index.js", "types": "./build/dts/index.d.ts"}, "./aggregates": {"import": "./build/aggregates/index.js", "require": "./build/aggregates/index.js", "types": "./build/dts/aggregates/index.d.ts"}, "./value-objects": {"import": "./build/value-objects/index.js", "require": "./build/value-objects/index.js", "types": "./build/dts/value-objects/index.d.ts"}, "./cache": {"import": "./build/cache/index.js", "require": "./build/cache/index.js", "types": "./build/dts/cache/index.d.ts"}, "./constants": {"import": "./build/constants/index.js", "require": "./build/constants/index.js", "types": "./build/dts/constants/index.d.ts"}, "./errors": {"import": "./build/errors/index.js", "require": "./build/errors/index.js", "types": "./build/dts/errors/index.d.ts"}, "./schemas": {"import": "./build/schemas/index.js", "require": "./build/schemas/index.js", "types": "./build/dts/schemas/index.d.ts"}, "./serializers": {"import": "./build/serializers/index.js", "require": "./build/serializers/index.js", "types": "./build/dts/serializers/index.d.ts"}, "./services": {"import": "./build/services/index.js", "require": "./build/services/index.js", "types": "./build/dts/services/index.d.ts"}, "./types": {"import": "./build/types/index.js", "require": "./build/types/index.js", "types": "./build/dts/types/index.d.ts"}, "./utils": {"import": "./build/utils/index.js", "require": "./build/utils/index.js", "types": "./build/dts/utils/index.d.ts"}}, "files": ["build", "build/dts"], "scripts": {"build": "tsc -b tsconfig.build.json && tsc-alias -p tsconfig.build.json", "dev": "tsc -b tsconfig.src.json -w && tsc-alias -p tsconfig.build.json", "test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc -b tsconfig.build.json", "lint": "pnpm biome check --write"}, "peerDependencies": {"effect": "^3.17.13"}, "devDependencies": {"@effect/vitest": "^0.12.1", "@rie/biome-config": "workspace:*", "@types/node": "^24.0.3", "@types/bun": "^1.2.21", "@types/pg": "^8.15.5", "tsc-alias": "^1.8.16", "typescript": "^5.9.2", "vitest": "^3.2.4"}, "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "@rie/api-contracts": "workspace:*", "@rie/constants": "workspace:*", "@rie/db-schema": "workspace:*", "@rie/typescript-config": "workspace:*", "@rie/utils": "workspace:*", "pg": "^8.16.3", "remeda": "^2.23.1"}}