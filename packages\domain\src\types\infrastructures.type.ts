import type {
  InfrastructureDataSchema,
  InfrastructureInputSchema,
  InfrastructureWithRelatedEquipmentsSchema,
} from '../schemas';

import type * as Schema from 'effect/Schema';

export type InfrastructureData = Schema.Schema.Type<
  typeof InfrastructureDataSchema
>;

export type InfrastructureWithRelatedEquipments = Schema.Schema.Type<
  typeof InfrastructureWithRelatedEquipmentsSchema
>;

export type InfrastructureInput = Schema.Schema.Type<
  typeof InfrastructureInputSchema
>;
