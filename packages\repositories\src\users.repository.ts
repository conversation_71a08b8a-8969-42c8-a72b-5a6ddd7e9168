import { people, users } from '@rie/db-schema/schemas';
import type { DbUserDetailWithRoles } from '@rie/domain/types';
import { PgDatabaseLive } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
// import type * as Schema from 'effect/Schema';

export class UsersRepositoryLive extends Effect.Service<UsersRepositoryLive>()(
  'UsersRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const dbClient = yield* PgDatabaseLive;

      /**
       * Find a user by ID with roles information
       */
      const findUserById = dbClient.makeQuery<
        string,
        DbUserDetailWithRoles | undefined
      >((execute, id: string) => {
        return execute((client) =>
          client.query.users.findFirst({
            where: eq(users.id, id),
            columns: {
              id: true,
              name: true,
              email: true,
              emailVerified: true,
              image: true,
              createdAt: true,
              updatedAt: true,
            },
            with: {
              userRoles: {
                columns: {
                  resourceType: true,
                  resourceId: true,
                  grantedBy: true,
                  createdAt: true,
                },
                with: {
                  role: {
                    columns: {
                      id: true,
                      name: true,
                      description: true,
                    },
                    with: {
                      rolePermissionGroups: {
                        with: {
                          group: {
                            with: {
                              permissions: {
                                with: {
                                  permission: true,
                                },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          }),
        );
      });

      /**
       * Find user permissions by userId
       * Includes both direct role permissions and permissions from permission groups
       */
      const findUserPermissions = dbClient.makeQuery(
        (execute, userId: string) => {
          return execute((client) =>
            client.query.users.findFirst({
              where: eq(users.id, userId),
              columns: {
                id: true,
              },
              with: {
                userRoles: {
                  columns: {
                    roleId: true,
                  },
                  with: {
                    role: {
                      columns: {
                        id: true,
                      },
                      with: {
                        // Direct role permissions
                        rolePermissions: {
                          columns: {
                            permissionId: true,
                          },
                          with: {
                            permission: {
                              columns: {
                                id: true,
                                domain: true,
                                action: true,
                              },
                            },
                          },
                        },
                        // Permissions from permission groups
                        rolePermissionGroups: {
                          columns: {
                            groupId: true,
                          },
                          with: {
                            group: {
                              columns: {
                                id: true,
                                name: true,
                              },
                              with: {
                                permissions: {
                                  columns: {
                                    permissionId: true,
                                  },
                                  with: {
                                    permission: {
                                      columns: {
                                        id: true,
                                        domain: true,
                                        action: true,
                                      },
                                    },
                                  },
                                },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            }),
          );
        },
      );

      /**
       * Find person associated with a user
       */
      const findPersonByUserId = dbClient.makeQuery(
        (execute, userId: string) => {
          return execute((client) =>
            client.query.people.findFirst({
              where: eq(people.userId, userId),
              columns: {
                id: true,
                firstName: true,
                lastName: true,
                uid: true,
              },
              with: {
                emails: {
                  columns: {
                    address: true,
                  },
                },
              },
            }),
          );
        },
      );

      return {
        findUserById,
        findUserPermissions,
        findPersonByUserId,
      } as const;
    }),
  },
) { }
