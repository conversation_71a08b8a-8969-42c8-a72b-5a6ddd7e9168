import { mapAddressResponseToAddressSchema } from '@/helpers/form-mappers';
import { getLocalizedValue } from '@/helpers/resources.helpers';
import type { VendorFormSchema } from '@/schemas/bottin/vendor-form-schema';
import type { ManufacturerFull } from '@/types/directory/manufacturer';
import type { SupportedLocale } from '@/types/locale';

export const mapManufacturerFullToFormSchema = (
  manufacturer: ManufacturerFull,
  currentLocale: SupportedLocale,
): VendorFormSchema => {
  const baseStructure: VendorFormSchema = {
    alias: getLocalizedValue(currentLocale, manufacturer.pseudonymes),
    contacts:
      manufacturer.emplacements !== null
        ? manufacturer.emplacements.map(mapAddressResponseToAddressSchema)
        : [],
    dateEnd: manufacturer.dateEnd ? new Date(manufacturer.dateEnd) : null,
    name: getLocalizedValue(currentLocale, manufacturer.names),
    phones: manufacturer.telephones.map((phone) => {
      return {
        phone: phone.text,
        description: getLocalizedValue(currentLocale, phone.descriptions),
      };
    }),
  };
  const id = manufacturer.id.toString();
  return { ...baseStructure, id };
};
