import { mapFormAddressToAddressPayload } from '@/helpers/form-mappers';
import { getPostLocalizedValue } from '@/helpers/resources.helpers';
import type { VendorFormSchema } from '@/schemas/bottin/vendor-form-schema';
import type { AddressPayload } from '@/types/common';
import type { EntityPayload } from '@/types/directory/directory';
import type { ManufacturerPostPayload } from '@/types/directory/manufacturer';

export const mapManufacturerBaseForm = (
  formData: VendorFormSchema,
): ManufacturerPostPayload => ({
  names: getPostLocalizedValue(formData.name),
  pseudonymes: getPostLocalizedValue(formData.alias),
  ...(formData.phones &&
    formData.phones.length > 0 && {
    telephones: formData.phones.map((phoneNumber) => ({
      descriptions: getPostLocalizedValue(phoneNumber.description),
      number: phoneNumber.phone?.toString() || '',
    })),
  }),
  ...(formData.dateEnd && {
    dateEnd: formData.dateEnd.toISOString(),
  }),
});

export function mapFormDataToPostPutPayload(
  formData: VendorFormSchema,
): EntityPayload<'vendor'> {
  const baseStructure = mapManufacturerBaseForm(formData);
  const manufacturerReception = formData.contacts
    .map(mapFormAddressToAddressPayload)
    .filter((v): v is AddressPayload => v !== null);

  return { ...baseStructure, emplacements: manufacturerReception };
}
