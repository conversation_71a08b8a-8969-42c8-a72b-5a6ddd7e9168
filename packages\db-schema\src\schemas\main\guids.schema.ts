import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import { pgTable, text, timestamp } from 'drizzle-orm/pg-core';
import { users } from '../auth';
import { infrastructures } from './infrastructures.schema';
import { people } from './people.schema';

export const guids = pgTable('guids', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  uuid: text().notNull().unique(),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const guidsRelations = relations(guids, ({ many }) => ({
  infrastructures: many(infrastructures),
  people: many(people),
}));
