import { DbUtils } from '@rie/utils';
import { relations } from 'drizzle-orm';
import { boolean, pgTable, text, timestamp, unique } from 'drizzle-orm/pg-core';
import { users } from '../auth';
import { locales } from './locales.schema';

export const visibilities = pgTable('visibilities', {
  id: text()
    .primaryKey()
    .$defaultFn(() => DbUtils.cuid2()),
  isActive: boolean().notNull().default(true),
  createdAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp({ withTimezone: true, mode: 'string' })
    .notNull()
    .defaultNow(),
  modifiedBy: text().references(() => users.id),
});

export const visibilitiesRelations = relations(visibilities, ({ many }) => ({
  translations: many(visibilitiesI18N),
}));

export const visibilitiesI18N = pgTable(
  'visibilities_i18n',
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => DbUtils.cuid2()),
    dataId: text()
      .notNull()
      .references(() => visibilities.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    locale: text()
      .notNull()
      .references(() => locales.code, { onUpdate: 'cascade' }),
    name: text(),
    description: text(),
  },
  (table) => [
    {
      uniqueDataIdLocale: unique().on(table.dataId, table.locale),
    },
  ],
);

export const visibilitiesI18NRelations = relations(
  visibilitiesI18N,
  ({ one }) => ({
    visibility: one(visibilities, {
      fields: [visibilitiesI18N.dataId],
      references: [visibilities.id],
    }),
    locale: one(locales, {
      fields: [visibilitiesI18N.locale],
      references: [locales.code],
    }),
  }),
);
