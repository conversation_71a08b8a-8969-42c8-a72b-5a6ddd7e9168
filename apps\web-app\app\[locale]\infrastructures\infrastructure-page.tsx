'use client';

import { InfrastructureGrid } from '@/app/[locale]/infrastructures/infrastructure-grid';
import { InfrastructureList } from '@/app/[locale]/infrastructures/infrastructure-list';
import { EntityPageHeader } from '@/components/entity-page/entity-page-header';
import { LoadingResource } from '@/components/loading-resource/loading-resource';
import { defaultRieServiceParams } from '@/constants/rie-client';
import { useGetInfiniteInfrastructures } from '@/hooks/infrastructure/useGetInfiniteInfrastructures';
import { useResetFiltersStateOnLeave } from '@/hooks/use-reset-filters-state-on-leave';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';
import { useURLQueryParamsToFilters } from '@/hooks/useURLQueryParamsToFilters';
import { useViewMode } from '@/hooks/useViewMode';
import { mapInfrastructuresToTableColumns } from '@/services/mappers/infrastructures';
import type { FacetValue } from '@/types/filters';
import type { Infrastructure } from '@/types/infrastructure';
import type { SupportedLocale } from '@/types/locale';
import { useTranslations } from 'next-intl';

type InfrastructurePageProps = {
  locale: SupportedLocale;
};

export const InfrastructurePage = ({ locale }: InfrastructurePageProps) => {
  const t = useTranslations('common');
  const { handleViewModeChange, viewMode } = useViewMode('cards');

  const {
    data: infiniteInfrastructures,
    fetchNextPage,
    fetchPreviousPage,
    hasNextPage,
    hasPreviousPage,
    isFetchingNextPage,
    isFetchingPreviousPage,
    status,
  } = useGetInfiniteInfrastructures({
    params: defaultRieServiceParams(locale),
    select: (infrastructures) => ({
      pageParams: infrastructures.pageParams,
      pages: infrastructures.pages.map((page) => ({
        ...page,
        data: mapInfrastructuresToTableColumns({
          count: page.count,
          data: page.data,
          facets: page.facets,
        }).data,
      })),
    }),
  });

  const { handleFirsRef, handleLastRef } = useInfiniteScroll({
    fetchNextPage,
    fetchPreviousPage,
    hasNextPage,
    hasPreviousPage,
    isFetchingNextPage,
    isFetchingPreviousPage,
  });

  // Always get facets even for table view to ensure filters work correctly
  const allFacets: Record<string, FacetValue[]> =
    infiniteInfrastructures?.pages[0].facets ?? {};

  // Always call useURLQueryParamsToFilters to sync URL params with store, even with empty facets
  // This ensures search and sort parameters are properly applied in both card and table views
  useURLQueryParamsToFilters(allFacets);
  useResetFiltersStateOnLeave();

  const allInfrastructures: Infrastructure[] =
    infiniteInfrastructures?.pages.flatMap((page) => page.data) ?? [];

  const infrastructuresCount = infiniteInfrastructures?.pages[0]?.count ?? 0;

  if (status === 'error') {
    return null;
  }

  return (
    <div className="w-full">
      {isFetchingPreviousPage && <LoadingResource />}
      <div ref={handleFirsRef} />
      <EntityPageHeader
        addResourceHref={'/infrastructures/ajouter'}
        addResourceLabel={t('addResource', {
          resource: t('infrastructure').toLowerCase(),
        })}
        countLabel={
          infiniteInfrastructures
            ? t('infrastructureCount', {
                count: infrastructuresCount,
              })
            : undefined
        }
        facets={allFacets ?? {}}
        onToggle={handleViewModeChange}
        resource="infrastructures"
        searchPlaceholder={t('searchInfrastructure')}
        viewMode={viewMode}
      />
      {status === 'pending' && <LoadingResource />}
      {viewMode === 'cards' ? (
        <>
          <InfrastructureGrid
            className="mt-13"
            infrastructures={allInfrastructures ?? []}
          />
          <div ref={handleLastRef} />
          {isFetchingNextPage && <LoadingResource />}
        </>
      ) : (
        <InfrastructureList locale={locale} />
      )}
    </div>
  );
};
