import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { INPUT_PATH, OUTPUT_PATH } from '../constants';
import type { Mapping } from '../types';
import { BaseConverter } from './base-converter';

interface MySqlInfrastructureI18n {
  id: number;
  data_id: number;
  language_id: string;
  nom: string | null;
  acronyme: string | null;
  description: string | null;
}

interface PostgresInfrastructureI18n {
  id: string;
  data_id: string;
  locale: string;
  name: string | null;
  description: string | null;
  otherNames: string | null;
  acronyms: string | null;
}

export class InfrastructureI18nMigrationConverter extends BaseConverter {
  private infrastructureI18nMappings: Mapping[] = [];
  private infrastructurePseudonyms: Record<string, string> = {};

  private parseInfrastructureI18NInsertStatement(
    sqlStatement: string,
    sqlContent: string,
  ): MySqlInfrastructureI18n[] {
    const values = this.extractValuesFromInsertStatement(
      sqlStatement,
      sqlContent,
      'infrastructure_trad',
    );

    // Check if values are valid
    if (!values.id || !values.data_id) {
      console.error('sqlStatement', sqlStatement);
      console.error('Invalid values:', values);
      return [];
    }

    try {
      return [
        {
          id: Number.parseInt(values.id),
          data_id: Number.parseInt(values.data_id),
          language_id: values.language_id,
          nom: values.nom,
          acronyme: values.acronyme,
          description: values.description,
        },
      ];
    } catch (error) {
      console.error('Error parsing values:', values, error);
      return [];
    }
  }

  private async loadInfrastructurePseudonyms(
    sqlContent: string,
  ): Promise<void> {
    // Extract INSERT statements for infrastructure table
    const insertStatements = this.extractInsertStatements(
      sqlContent,
      'infrastructure',
    );

    for (const statement of insertStatements) {
      const values = this.extractValuesFromInsertStatement(
        statement,
        sqlContent,
        'infrastructure',
      );
      if (values.infrastructure_id && values.pseudonyme) {
        this.infrastructurePseudonyms[values.infrastructure_id] =
          values.pseudonyme;
      }
    }
  }

  private convertToPostgres(
    mysqlRecord: MySqlInfrastructureI18n,
    infrastructureIdMappings: Record<string, string>,
  ): PostgresInfrastructureI18n {
    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the infrastructure
    const newInfrastructureId =
      infrastructureIdMappings[mysqlRecord.data_id.toString()];
    if (!newInfrastructureId) {
      throw new Error(
        `No mapping found for infrastructure_id: ${mysqlRecord.data_id}`,
      );
    }
    // Store mapping for future reference
    this.infrastructureI18nMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: postgresId,
    });

    // Get otherNames from the infrastructure table
    const otherNames =
      this.infrastructurePseudonyms[mysqlRecord.data_id] ?? null;

    return {
      id: postgresId,
      data_id: newInfrastructureId,
      locale: mysqlRecord.language_id,
      name: mysqlRecord.nom,
      description: mysqlRecord.description,
      otherNames: otherNames,
      acronyms: mysqlRecord.acronyme,
    };
  }

  async convertFile(): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(INPUT_PATH, 'utf8');

      // Load pseudonyms from infrastructure table
      await this.loadInfrastructurePseudonyms(sqlContent);

      // Extract INSERT statements for infrastructure_trad table (old MySQL table name)
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'infrastructure_trad',
      );

      if (insertStatements.length === 0) {
        console.log('No infrastructure_trad INSERT statements found.');
        return;
      }

      // Load infrastructure ID mappings
      const infrastructureIdMappings =
        await this.loadEntityIdMappings('infrastructure');

      const allPostgresRecords: PostgresInfrastructureI18n[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        // Parse the MySQL records from the statement
        const mysqlRecords = this.parseInfrastructureI18NInsertStatement(
          statement,
          sqlContent,
        );

        // Skip empty records
        if (mysqlRecords.length === 0) {
          continue;
        }

        // Convert each record to PostgreSQL format
        const postgresRecords = [];
        for (const record of mysqlRecords) {
          try {
            // Check if we have a mapping for this infrastructure ID
            if (!infrastructureIdMappings[record.data_id.toString()]) {
              console.log(
                `No mapping found for infrastructure_id: ${record.data_id}, skipping record`,
              );
              continue;
            }

            const postgresRecord = this.convertToPostgres(
              record,
              infrastructureIdMappings,
            );
            postgresRecords.push(postgresRecord);
          } catch (error) {
            console.error('Error converting record:', record, error);
          }
        }

        allPostgresRecords.push(...postgresRecords);
      }

      const columnNames = [
        'id',
        'data_id',
        'locale',
        'name',
        'description',
        'other_names',
        'acronyms',
      ];

      const postgresInsertsWithMappings =
        this.generatePostgresWithColumnsI18NInsert(
          allPostgresRecords,
          'infrastructures_i18n',
          'Infrastructure I18n Inserts',
          columnNames,
        );

      // Create the output directory if it doesn't exist
      const outputDir = path.dirname(OUTPUT_PATH);
      await this.safeMkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await this.safeAppendFile(OUTPUT_PATH, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.infrastructureI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'infrastructure_trad', postgres: 'infrastructures_i18n' },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRecords.length} infrastructures_i18n records`,
      );
      console.log(`- PostgreSQL inserts written to: ${OUTPUT_PATH}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
