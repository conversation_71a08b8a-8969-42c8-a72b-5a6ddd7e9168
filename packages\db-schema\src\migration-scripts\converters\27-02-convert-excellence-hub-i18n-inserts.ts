import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { INPUT_PATH, OUTPUT_PATH } from '../constants';
import { type I18NColumnMapper, i18nColumnMapper } from '../constants';
import type {
  Mapping,
  MySQLI18NDescription,
  PostgresI18NDescription,
} from '../types';
import { BaseConverter } from './base-converter';

export class ExcellenceHubI18nMigrationConverter extends BaseConverter {
  private excellenceHubI18nMappings: Mapping[] = [];

  private convertToPostgres(
    mysqlRecord: MySQLI18NDescription,
    excellenceHubIdMappings: Record<string, string>,
  ): PostgresI18NDescription {
    const postgresId = this.generateCuid2();

    // Get the new PostgreSQL ID for the excellence_hub
    const newExcellenceHubId =
      excellenceHubIdMappings[mysqlRecord.data_id.toString()];
    if (!newExcellenceHubId) {
      throw new Error(
        `No mapping found for excellence_hub_id: ${mysqlRecord.data_id}`,
      );
    }
    // Store mapping for future reference
    this.excellenceHubI18nMappings.push({
      mysqlId: mysqlRecord.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
      data_id: newExcellenceHubId,
      locale: mysqlRecord.locale,
      name: mysqlRecord.nom,
      description: mysqlRecord.description,
    };
  }

  async convertFile(): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(INPUT_PATH, 'utf8');

      // Extract INSERT statements for pole_excellence_trad table (old MySQL table name)
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'pole_excellence_trad',
      );

      if (insertStatements.length === 0) {
        console.log('No pole_excellence_trad INSERT statements found.');
        return;
      }

      // Load excellence_hub ID mappings
      const excellenceHubIdMappings =
        await this.loadEntityIdMappings('pole_excellence');

      const allPostgresRecords: PostgresI18NDescription[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlRecords = this.parseI18NInsertStatement(
          statement,
          sqlContent,
          'pole_excellence_trad',
        );
        const postgresRecords = mysqlRecords.map((record) => {
          return this.convertToPostgres(record, excellenceHubIdMappings);
        });

        allPostgresRecords.push(...postgresRecords);
      }

      const createStatement = this.extractCreateStatement(
        sqlContent,
        'pole_excellence_trad',
      );
      const columns = this.extractColumnNames(createStatement).map(
        (column) => i18nColumnMapper[column as I18NColumnMapper] ?? column,
      );

      // Generate output
      const postgresInserts = this.generatePostgresWithColumnsI18NInsert(
        allPostgresRecords,
        'excellence_hubs_i18n',
        'Excellence Hub I18n Inserts',
        columns,
      );

      // Create the output directory if it doesn't exist
      const outputDir = path.dirname(OUTPUT_PATH);
      await this.safeMkdir(outputDir, { recursive: true });

      // Write the SQL content to the output file
      await this.safeAppendFile(OUTPUT_PATH, postgresInserts);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.excellenceHubI18nMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        {
          mysql: 'pole_excellence_trad',
          postgres: 'excellence_hubs_i18n',
        },
        idMappings,
      );

      console.log('Conversion completed successfully!');
      console.log(
        `- Found ${allPostgresRecords.length} excellence_hubs_i18n records`,
      );
    } catch (error) {
      console.error('Error converting file:', error);
      throw error;
    }
  }
}
