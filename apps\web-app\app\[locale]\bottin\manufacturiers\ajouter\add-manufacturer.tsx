'use client';

import { ManufacturerForm } from '@/app/[locale]/bottin/manufacturiers/form/manufacturer-form';
import { manufacturerFormDefaultValues } from '@/constants/directory/manufacturer';
import { useCreateVendor } from '@/hooks/directory/vendors.hook';
import type { VendorFormSchema } from '@/schemas/bottin/vendor-form-schema';
import type { SupportedLocale } from '@/types/locale';

type AddManufacturerProps = {
  locale: SupportedLocale;
  formSections: Record<string, string>;
};
export const AddManufacturer = ({
  locale,
  formSections,
}: AddManufacturerProps) => {
  const { mutate, status } = useCreateVendor();
  const onSubmit = (data: VendorFormSchema) => {
    mutate(data);
  };
  
  return (
    <ManufacturerForm
      defaultValues={manufacturerFormDefaultValues(locale)}
      formSections={formSections}
      onSubmit={onSubmit}
      status={status}
    />
  );
};
