import * as fs from 'node:fs/promises';
import { INPUT_PATH, OUTPUT_PATH } from '../constants';
import { logConverterRecordCounts } from '../converter-validator';
import type {
  Mapping,
  MySqlI18NBaseReturn,
  PostgresI18NBaseReturn,
} from '../types';
import { BaseConverter } from './base-converter';

export class ResearchFieldMigrationConverter extends BaseConverter {
  private researchFieldMappings: Mapping[] = [];

  private parseInsertStatement(
    sqlStatement: string,
    sqlContent: string,
  ): MySqlI18NBaseReturn[] {
    const values = this.extractValuesFromInsertStatement(
      sqlStatement,
      sqlContent,
      'domaine_recherche',
    );

    return [
      {
        id: Number.parseInt(values.id),
      },
    ];
  }

  private convertToPostgres(
    mysqlResearchField: MySqlI18NBaseReturn,
  ): PostgresI18NBaseReturn {
    const postgresId = this.generateCuid2();

    // Store mapping for future reference
    this.researchFieldMappings.push({
      mysqlId: mysqlResearchField.id,
      postgresId: postgresId,
    });

    return {
      id: postgresId,
    };
  }

  async convertFile(): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(INPUT_PATH, 'utf8');

      // Extract INSERT statements for domaine_recherche table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'domaine_recherche',
      );

      if (insertStatements.length === 0) {
        console.log('No domaine_recherche INSERT statements found.');
        return;
      }

      const allPostgresResearchFields: PostgresI18NBaseReturn[] = [];

      // Process each INSERT statement
      for (const statement of insertStatements) {
        const mysqlResearchFields = this.parseInsertStatement(
          statement,
          sqlContent,
        );
        const postgresResearchFields = mysqlResearchFields.map((rf) =>
          this.convertToPostgres(rf),
        );
        allPostgresResearchFields.push(...postgresResearchFields);
      }

      // Generate output with both inserts and mappings
      const postgresInsertsWithMappings =
        this.generatePostgresBaseTableInsertWithMappings(
          allPostgresResearchFields,
          'research_fields',
          'Research Field Inserts',
        );

      // Create the output directory if it doesn't exist
      const outputDir = OUTPUT_PATH.substring(0, OUTPUT_PATH.lastIndexOf('/'));
      await this.safeMkdir(outputDir, { recursive: true });

      // Append the SQL content to the output file
      await this.safeAppendFile(OUTPUT_PATH, postgresInsertsWithMappings);

      // Create ID mappings object
      const idMappings = Object.fromEntries(
        this.researchFieldMappings.map((mapping) => [
          mapping.mysqlId.toString(),
          mapping.postgresId,
        ]),
      );

      // Write mappings to JSON file
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'domaine_recherche', postgres: 'research_fields' },
        idMappings,
      );

      console.log('Conversion completed successfully!');

      // Validate the conversion - this will log any record count differences
      await logConverterRecordCounts('ResearchFieldMigrationConverter', [
        { mysql: 'domaine_recherche', postgres: 'research_fields' },
      ]);
      console.log(
        `- Found ${allPostgresResearchFields.length} research_fields records`,
      );
      console.log(`- PostgreSQL inserts written to: ${OUTPUT_PATH}`);
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
