import { CharacterCount } from '@/components/character-count/character-count';
import { FieldInfo } from '@/components/FieldInfo';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import type { VendorFormSchema } from '@/schemas/bottin/vendor-form-schema';
import { FormControl, FormField, FormItem, FormMessage } from '@/ui/form';
import { Input } from '@/ui/input';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

type PhoneFieldProps = {
  index: number;
};

export const PhoneField = ({ index }: PhoneFieldProps) => {
  const t = useTranslations('directory.form.sections.description.generalInfo');
  const { control } = useFormContext<VendorFormSchema>();

  return (
    <FormField
      control={control}
      name={`phones.${index}.phone`}
      render={({ field }) => (
        <FormItem>
          <LabelTooltip
            htmlFor={`phones.${index}.phone`}
            label={t('fields.phone.label')}
          />
          <FormControl>
            <Input {...field} id={`phone-${index}`} />
          </FormControl>
          <FieldInfo>
            <FormMessage />
            <CharacterCount count={field.value?.length ?? 0} max={15} />
          </FieldInfo>
        </FormItem>
      )}
    />
  );
};
