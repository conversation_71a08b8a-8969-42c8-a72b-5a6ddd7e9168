import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { INPUT_PATH, OUTPUT_PATH } from '../constants';
import { logConverterRecordCounts } from '../converter-validator';
import type { Mapping } from '../types';
import { BaseConverter } from './base-converter';

interface MySqlInfrastructureReception {
  infrastructure_id: number;
  batiment_id: number | null;
  local_id: number | null;
  territory_id: number | null;
}

interface PostgresCampusAddress {
  id: string;
  building_id: string | null;
  room_id: string | null; // Changed from 'room' to 'room_id' to match schema
}

interface PostgresAddress {
  id: string;
  address_type: string;
  campus_address_id: string;
  civic_address_id: null;
}

export class InfrastructureAddressesMigrationConverter extends BaseConverter {
  private campusAddressMappings: Mapping[] = [];
  private addressMappings: Mapping[] = [];
  private roomMappings: Record<string, string> = {};
  private buildingMappings: Record<string, string> = {};

  async convertFile(): Promise<void> {
    try {
      // Read input SQL file
      const sqlContent = await fs.readFile(INPUT_PATH, 'utf8');

      // Extract INSERT statements for infrastructure_reception table
      const insertStatements = this.extractInsertStatements(
        sqlContent,
        'infrastructure_reception',
      );

      if (insertStatements.length === 0) {
        console.log('No infrastructure_reception INSERT statements found.');
        return;
      }

      const infrastructureMappings =
        await this.loadEntityIdMappings('infrastructure');

      // Load room ID mappings (from local -> rooms converter)
      this.roomMappings = await this.loadEntityIdMappings('local');
      this.buildingMappings = await this.loadEntityIdMappings('batiment');

      console.log(
        `Loaded ${Object.keys(this.roomMappings).length} room mappings`,
      );
      console.log(
        `Loaded ${Object.keys(this.buildingMappings).length} building mappings`,
      );
      console.log(
        `Loaded ${Object.keys(infrastructureMappings).length} infrastructure mappings`,
      );

      if (Object.keys(this.roomMappings).length === 0) {
        console.warn(
          '⚠️  No room mappings found. Run room converter (31-01) first.',
        );
      }
      if (Object.keys(infrastructureMappings).length === 0) {
        console.warn(
          '⚠️  No infrastructure mappings found. Run infrastructure converter (25-01) first.',
        );
      }

      const campusAddresses: PostgresCampusAddress[] = [];
      const addresses: PostgresAddress[] = [];
      const infrastructureAddressUpdates: {
        infrastructureId: string;
        addressId: string;
      }[] = [];

      // Process each INSERT statement (already split into individual statements by base converter)
      for (const statement of insertStatements) {
        // Extract values from this single INSERT statement
        const values = this.extractValuesFromInsertStatement(
          statement,
          sqlContent,
          'infrastructure_reception',
        );

        // Convert to our record format - values is already an object with column names as keys
        const record: MySqlInfrastructureReception = {
          infrastructure_id: Number.parseInt(values.infrastructure_id),
          batiment_id:
            values.batiment_id === 'NULL' || values.batiment_id === null
              ? null
              : Number.parseInt(values.batiment_id),
          local_id:
            values.local_id === 'NULL' || values.local_id === null
              ? null
              : Number.parseInt(values.local_id),
          territory_id:
            values.territory_id === 'NULL' || values.territory_id === null
              ? null
              : Number.parseInt(values.territory_id),
        };

        // Create campus address
        const campusAddressId = this.generateCuid2();

        const roomInfo =
          record.local_id !== null
            ? (this.roomMappings[record.local_id] ?? null)
            : null;

        const buildingIfo =
          record.batiment_id !== null
            ? (this.buildingMappings[record.batiment_id.toString()] ?? null)
            : null;

        campusAddresses.push({
          id: campusAddressId,
          building_id: buildingIfo,
          room_id: roomInfo,
        });

        this.campusAddressMappings.push({
          mysqlId: record.infrastructure_id,
          postgresId: campusAddressId,
        });

        // Create address
        const addressId = this.generateCuid2();
        addresses.push({
          id: addressId,
          address_type: 'campus',
          campus_address_id: campusAddressId,
          civic_address_id: null,
        });

        this.addressMappings.push({
          mysqlId: record.infrastructure_id,
          postgresId: addressId,
        });

        // Get infrastructure ID
        const infrastructureId =
          infrastructureMappings[record.infrastructure_id.toString()];
        if (infrastructureId) {
          infrastructureAddressUpdates.push({
            infrastructureId,
            addressId,
          });
        } else {
          console.warn(
            `No mapping found for infrastructure_id: ${record.infrastructure_id}`,
          );
        }
      }

      const campusAddressInsertsWithMappings =
        this.generatePostgresWithColumnsI18NInsert(
          campusAddresses,
          'campus_addresses',
          'Campus Addresses Inserts',
          ['id', 'building_id', 'room_id'],
        );

      const addressInsertsWithMappings =
        this.generatePostgresWithColumnsI18NInsert(
          addresses,
          'addresses',
          'Addresses Inserts',
          ['id', 'address_type', 'campus_address_id', 'civic_address_id'], // Correct column names
        );

      // Generate SQL for infrastructure updates
      let infrastructureAddresses = this.generateCommentHeader(
        'Infrastructure Address Updates',
      );
      for (const update of infrastructureAddressUpdates) {
        infrastructureAddresses += `UPDATE "infrastructures" SET "address_id" = '${update.addressId}' WHERE "id" = '${update.infrastructureId}';\n`;
      }

      // Write the SQL content to the output file
      await this.safeAppendFile(OUTPUT_PATH, campusAddressInsertsWithMappings);
      await this.safeAppendFile(OUTPUT_PATH, addressInsertsWithMappings);
      await this.safeAppendFile(OUTPUT_PATH, infrastructureAddresses);

      // Write mappings to JSON files
      const outputDir = path.dirname(OUTPUT_PATH);
      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'infrastructure_reception', postgres: 'campus_addresses' },
        Object.fromEntries(
          this.campusAddressMappings.map((m) => [
            m.mysqlId.toString(),
            m.postgresId,
          ]),
        ),
      );

      await this.writeMappingsToJson(
        outputDir,
        { mysql: 'infrastructure_reception', postgres: 'addresses' },
        Object.fromEntries(
          this.addressMappings.map((m) => [m.mysqlId.toString(), m.postgresId]),
        ),
      );

      console.log('Conversion completed successfully!');

      // Validate the conversion - this will log any record count differences
      await logConverterRecordCounts(
        'InfrastructureAddressesMigrationConverter',
        [
          { mysql: 'infrastructure_reception', postgres: 'campus_addresses' },
          { mysql: 'infrastructure_reception', postgres: 'addresses' },
        ],
      );
      console.log(
        `- Created ${campusAddresses.length} campus_addresses records`,
      );
      console.log(`- Created ${addresses.length} addresses records`);
      console.log(
        `- Updated ${infrastructureAddressUpdates.length} infrastructure records`,
      );
    } catch (error) {
      console.error('Error during conversion:', error);
      throw error;
    }
  }
}
