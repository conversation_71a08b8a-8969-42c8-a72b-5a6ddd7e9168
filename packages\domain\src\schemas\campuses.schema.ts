import {
  DbCampusI18NSelectSchema,
  DbCampusInputSchema,
  DbCampusSelectSchema,
} from '@rie/db-schema/entity-schemas';
import type { DbCampusI18N } from '@rie/db-schema/entity-types';
import * as Schema from 'effect/Schema';
import { requiredFieldWth150MaxLengthSchema } from './base.schema';
import { LocaleSchema } from './query.schema';
import { createDbTranslationSchema } from './translation.schema';

// — Full Campus shape
export const CampusSchema = Schema.Struct({
  ...DbCampusSelectSchema.omit('modifiedBy').fields,
  modifiedBy: Schema.NullishOr(Schema.String),
  translations: Schema.Array(DbCampusI18NSelectSchema.omit('id', 'dataId')),
});

// — Translation input schema
export const CampusI18NInputSchema = Schema.Struct({
  locale: LocaleSchema,
  name: requiredFieldWth150MaxLengthSchema('Name'),
});

// — Campus List view schema (for directory table)
export const CampusListSchema = Schema.Struct({
  id: Schema.String,
  text: Schema.String, // nom par défaut depuis traductions
  jurisdiction: Schema.NullishOr(Schema.String), // institution associée
  lastUpdatedAt: Schema.String,
  // Champs internes pour les actions
  sadId: Schema.NullishOr(Schema.String),
  institutionId: Schema.NullishOr(Schema.String),
});

// — Campus Select view schema
export const CampusSelectSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

// — Campus Edit view schema
export const CampusEditSchema = Schema.Struct({
  id: Schema.String,
  sadId: Schema.NullishOr(Schema.String),
  institutionId: Schema.NullishOr(Schema.String),
  translations: Schema.Array(CampusI18NInputSchema),
});

// — Campus Detail view schema (same as list for now)
export const CampusDetailSchema = CampusListSchema;

// API Input Schema - for external API validation (excludes dataId)
export const DbCampusI18NInputSchema = createDbTranslationSchema<DbCampusI18N>({
  fields: {
    name: {
      required: true,
      maxLength: 150,
    },
  },
});

// Domain Creation Schema - for domain object creation (includes dataId)
export const DbCampusI18NCreationSchema = Schema.extend(
  DbCampusI18NInputSchema,
  Schema.Struct({
    dataId: Schema.String,
  }),
);

// Full Data Schema - for complete database records (includes id and dataId)
export const DbCampusI18NDataSchema = Schema.extend(
  DbCampusI18NCreationSchema,
  Schema.Struct({
    id: Schema.String,
  }),
);

export const DbCampusDataSchema = Schema.Struct({
  ...DbCampusSelectSchema.fields,
  translations: Schema.Array(DbCampusI18NDataSchema),
});

// — Input (create/update) shape
export const CampusInputSchema = Schema.Struct({
  ...DbCampusInputSchema.omit('id').fields,
  translations: Schema.Array(DbCampusI18NInputSchema),
});
