import { DBSchema } from '@rie/db-schema';
import { Vendor } from '@rie/domain/aggregates';
import type { VendorData } from '@rie/domain/types';
import { PgDatabaseLive } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';

export class VendorsRepositoryLive extends Effect.Service<VendorsRepositoryLive>()(
  'VendorsRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const dbClient = yield* PgDatabaseLive;

      const findAllVendors = () => {
        const getAllVendorsEffect = dbClient.makeQuery((execute) =>
          execute((client) =>
            client.query.vendors.findMany({
              columns: {
                id: true,
                isActive: true,
                startDate: true,
                endDate: true,
                createdAt: true,
                updatedAt: true,
                modifiedBy: true,
              },
              with: {
                translations: {
                  columns: {
                    dataId: true,
                    id: true,
                    locale: true,
                    name: true,
                    website: true,
                    description: true,
                    otherNames: true,
                  },
                },
              },
            }),
          ),
        );

        const rawVendorsEffect = getAllVendorsEffect();
        return pipe(
          rawVendorsEffect,
          Effect.tap((rows) =>
            Effect.sync(() =>
              console.log(
                `[Repo][Vendors] fetched ${rows.length} rows from DB`,
              ),
            ),
          ),
          Effect.flatMap((rawVendors) =>
            // Use Effect.all to hydrate every raw vendor in the array in parallel
            Effect.all(
              rawVendors.map((rawVendor) =>
                Vendor.fromDatabaseData(rawVendor as VendorData),
              ),
            ),
          ),
          Effect.tap((aggregates) =>
            Effect.sync(() =>
              console.log(
                `[Repo][Vendors] hydrated ${aggregates.length} aggregates`,
              ),
            ),
          ),
        );
      };

      const findVendorById = (id: string) => {
        const getVendorByIdEffect = dbClient.makeQuery((execute) =>
          execute((client) =>
            client.query.vendors.findFirst({
              where: eq(DBSchema.vendors.id, id),
              columns: {
                id: true,
                isActive: true,
                startDate: true,
                endDate: true,
                createdAt: true,
                updatedAt: true,
                modifiedBy: true,
              },
              with: {
                translations: {
                  columns: {
                    dataId: true,
                    id: true,
                    locale: true,
                    name: true,
                    website: true,
                    description: true,
                    otherNames: true,
                  },
                },
              },
            }),
          ),
        );

        const rawVendorEffect = getVendorByIdEffect();
        return pipe(
          rawVendorEffect,
          Effect.flatMap((rawVendor) => {
            if (!rawVendor) {
              return Effect.succeed(null);
            }
            return pipe(
              Vendor.fromDatabaseData(rawVendor as VendorData),
              Effect.map((vendor) => vendor),
            );
          }),
        );
      };

      const save = (vendor: Vendor) =>
        pipe(
          vendor.toRaw(), // Dehydrate the aggregate to a plain data object, this also validates its state.
          Effect.flatMap((vendorData) =>
            pipe(
              dbClient.transaction((tx) =>
                Effect.gen(function* () {
                  // Use Drizzle's insert...onConflictDoUpdate to handle both cases atomically.
                  const [savedVendor] = yield* tx((client) =>
                    client
                      .insert(DBSchema.vendors)
                      .values(vendorData)
                      .onConflictDoUpdate({
                        target: DBSchema.vendors.id,
                        set: {
                          isActive: vendorData.isActive,
                          startDate: vendorData.startDate,
                          endDate: vendorData.endDate,
                          modifiedBy: vendorData.modifiedBy,
                        },
                      })
                      .returning({ id: DBSchema.vendors.id }),
                  );

                  if (!savedVendor) {
                    return yield* Effect.fail(
                      new Error('Failed to save vendor to database'),
                    );
                  }

                  // Replace all translations for this vendor.
                  yield* tx((client) =>
                    client
                      .delete(DBSchema.vendorsI18N)
                      .where(eq(DBSchema.vendorsI18N.dataId, savedVendor.id)),
                  );

                  // Insert translations with the correct dataId and return the result
                  if (vendorData.translations.length > 0) {
                    yield* tx((client) =>
                      client.insert(DBSchema.vendorsI18N).values(
                        vendorData.translations.map((t) => ({
                          ...t,
                          dataId: savedVendor.id, // Use the actual saved vendor ID
                        })),
                      ),
                    );
                  }

                  // For new vendor creation, just re-fetch to get proper timestamps and IDs
                  // But do it outside the transaction using findVendorById
                  return savedVendor.id;
                }),
              ),
              Effect.flatMap((vendorId) => findVendorById(vendorId)),
              Effect.flatMap((vendor) => {
                if (!vendor) {
                  return Effect.fail(
                    new Error('Failed to retrieve saved vendor'),
                  );
                }
                return Effect.succeed(vendor);
              }),
            ),
          ),
        );



      const deleteVendor = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client
            .delete(DBSchema.vendors)
            .where(eq(DBSchema.vendors.id, id))
            .returning({ id: DBSchema.vendors.id }),
        );
      });

      return {
        findAllVendors,
        findVendorById,
        save,
        deleteVendor,
      } as const;
    }),
  },
) { }
