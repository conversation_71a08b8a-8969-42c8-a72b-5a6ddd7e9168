import {
  DbUnitInputSchema,
  DbUnitSelectSchema,
} from '@rie/db-schema/entity-schemas';
import type { DbUnitI18N } from '@rie/db-schema/entity-types';
import * as Schema from 'effect/Schema';
import {
  optionalFieldWth1500MaxLengthSchema,
  requiredFieldWth150MaxLengthSchema,
} from './base.schema';
import { LocaleSchema } from './query.schema';
import { createDbTranslationSchema } from './translation.schema';

// — Full Unit shape (with translations)
export const UnitSchema = Schema.Struct({
  ...DbUnitSelectSchema.fields,
  translations: Schema.Array(DbUnitSelectSchema),
});

// — Translation Input for API
export const UnitI18NInputSchema = Schema.Struct({
  locale: LocaleSchema,
  name: requiredFieldWth150MaxLengthSchema('Name'),
  description: optionalFieldWth1500MaxLengthSchema('Description'),
  otherNames: optionalFieldWth1500MaxLengthSchema('Other Names'),
  acronyms: optionalFieldWth1500MaxLengthSchema('Acronyms'),
});

// — Unit List view schema (for directory table)
export const UnitListSchema = Schema.Struct({
  id: Schema.String,
  name: Schema.String, // nom par défaut depuis traductions
  type: Schema.NullishOr(Schema.String), // type d'unité
  parent: Schema.NullishOr(Schema.String), // unité parente
  lastUpdatedAt: Schema.String,
  // Champs internes pour les actions
  guidId: Schema.NullishOr(Schema.String),
  typeId: Schema.String,
  parentId: Schema.NullOr(Schema.String),
});

// — Unit Select view schema
export const UnitSelectSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

// — Unit Edit view schema
export const UnitEditSchema = Schema.Struct({
  id: Schema.String,
  guidId: Schema.NullishOr(Schema.String),
  typeId: Schema.String,
  parentId: Schema.NullOr(Schema.String),
  translations: Schema.Array(UnitI18NInputSchema),
});

// — Unit Detail view schema (same as list for now)
export const UnitDetailSchema = UnitListSchema;

// API Input Schema - for external API validation (excludes dataId)
export const DbUnitI18NInputSchema = createDbTranslationSchema<DbUnitI18N>({
  fields: {
    name: {
      required: true,
      maxLength: 250,
    },
    description: {
      required: false,
      maxLength: 1500,
    },
    otherNames: {
      required: false,
      maxLength: 1500,
    },
    acronyms: {
      required: false,
      maxLength: 1500,
    },
  },
});

// Domain Creation Schema - for domain object creation (includes dataId)
export const DbUnitI18NCreationSchema = Schema.extend(
  DbUnitI18NInputSchema,
  Schema.Struct({
    dataId: Schema.String,
  }),
);

// Full Data Schema - for complete database records (includes id and dataId)
export const DbUnitI18NDataSchema = Schema.extend(
  DbUnitI18NCreationSchema,
  Schema.Struct({
    id: Schema.String,
  }),
);

export const DbUnitDataSchema = Schema.Struct({
  ...DbUnitSelectSchema.fields,
  translations: Schema.Array(DbUnitI18NDataSchema),
});

export const UnitDetailsDataSchema = Schema.Struct({
  ...DbUnitSelectSchema.fields,
  translations: Schema.Struct({
    name: Schema.String,
    description: Schema.NullOr(Schema.String),
    otherNames: Schema.NullOr(Schema.String),
    acronyms: Schema.NullOr(Schema.String),
  }),
});

// — Input (create/update) shape
export const UnitInputSchema = Schema.Struct({
  ...DbUnitInputSchema.omit('id').fields,
  translations: Schema.Array(DbUnitI18NInputSchema),
});
