import type { BuildingFull } from '@/types/building';

export const buildingsMock: BuildingFull[] = [
  {
    campus: {
      etablissement: {
        id: 'G000001',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        text: 'Université de Montréal',
        uid: 'G000001',
      },
      id: '1',
      nom: 'Campus principal',
      noms: {
        fr: 'Campus principal',
      },
      pseudonyme: null,
      slug: 'campus-principal',
      text: 'Campus principal',
      uid: 'mtl',
    },
    id: '1',
    juridiction: {
      acronym: null,
      acronyms: {
        en: null,
        fr: null,
      },
      createdAt: '2023-10-16T21:48:30-0400',
      dateBegin: null,
      dateEnd: null,
      id: 'G000001',
      lastUpdatedAt: '2023-10-16T21:48:30-0400',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
      organizationId: 1,
      parent: null,
      pseudonymes: {
        en: null,
        fr: null,
      },
      text: 'Université de Montréal',
      typeEtablissement: {
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      uid: 'G000001',
    },
    nom: 'Samuel-Bronfman',
    noms: {
      fr: 'Samuel-Bronfman',
    },
    pseudonyme: null,
    slug: null,
    text: 'Samuel-Bronfman - Campus principal',
    uid: '504A',
  },
  {
    campus: {
      etablissement: {
        id: 'G000001',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        text: 'Université de Montréal',
        uid: 'G000001',
      },
      id: '1',
      nom: 'Campus principal',
      noms: {
        fr: 'Campus principal',
      },
      pseudonyme: null,
      slug: 'campus-principal',
      text: 'Campus principal',
      uid: 'mtl',
    },
    id: '2',
    juridiction: {
      acronym: null,
      acronyms: {
        en: null,
        fr: null,
      },
      createdAt: '2023-10-16T21:48:30-0400',
      dateBegin: null,
      dateEnd: null,
      id: 'G000001',
      lastUpdatedAt: '2023-10-16T21:48:30-0400',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
      organizationId: 1,
      parent: null,
      pseudonymes: {
        en: null,
        fr: null,
      },
      text: 'Université de Montréal',
      typeEtablissement: {
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      uid: 'G000001',
    },
    nom: 'Roger-Gaudry',
    noms: {
      fr: 'Roger-Gaudry',
    },
    pseudonyme: null,
    slug: null,
    text: 'Roger-Gaudry - Campus principal',
    uid: '511A',
  },
  {
    campus: {
      etablissement: {
        id: 'G000001',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        text: 'Université de Montréal',
        uid: 'G000001',
      },
      id: '1',
      nom: 'Campus principal',
      noms: {
        fr: 'Campus principal',
      },
      pseudonyme: null,
      slug: 'campus-principal',
      text: 'Campus principal',
      uid: 'mtl',
    },
    id: '3',
    juridiction: {
      acronym: null,
      acronyms: {
        en: null,
        fr: null,
      },
      createdAt: '2023-10-16T21:48:30-0400',
      dateBegin: null,
      dateEnd: null,
      id: 'G000001',
      lastUpdatedAt: '2023-10-16T21:48:30-0400',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
      organizationId: 1,
      parent: null,
      pseudonymes: {
        en: null,
        fr: null,
      },
      text: 'Université de Montréal',
      typeEtablissement: {
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      uid: 'G000001',
    },
    nom: 'Pavillon Claire-McNicoll',
    noms: {
      fr: 'Pavillon Claire-McNicoll',
    },
    pseudonyme: null,
    slug: null,
    text: 'Pavillon Claire-McNicoll - Campus principal',
    uid: '511C',
  },
  {
    campus: {
      etablissement: {
        id: 'G000001',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        text: 'Université de Montréal',
        uid: 'G000001',
      },
      id: '1',
      nom: 'Campus principal',
      noms: {
        fr: 'Campus principal',
      },
      pseudonyme: null,
      slug: 'campus-principal',
      text: 'Campus principal',
      uid: 'mtl',
    },
    id: '4',
    juridiction: {
      acronym: null,
      acronyms: {
        en: null,
        fr: null,
      },
      createdAt: '2023-10-16T21:48:30-0400',
      dateBegin: null,
      dateEnd: null,
      id: 'G000001',
      lastUpdatedAt: '2023-10-16T21:48:30-0400',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
      organizationId: 1,
      parent: null,
      pseudonymes: {
        en: null,
        fr: null,
      },
      text: 'Université de Montréal',
      typeEtablissement: {
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      uid: 'G000001',
    },
    nom: 'Centrale Thermique',
    noms: {
      fr: 'Centrale Thermique',
    },
    pseudonyme: null,
    slug: null,
    text: 'Centrale Thermique - Campus principal',
    uid: '512A',
  },
  {
    campus: {
      etablissement: {
        id: 'G000001',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        text: 'Université de Montréal',
        uid: 'G000001',
      },
      id: '1',
      nom: 'Campus principal',
      noms: {
        fr: 'Campus principal',
      },
      pseudonyme: null,
      slug: 'campus-principal',
      text: 'Campus principal',
      uid: 'mtl',
    },
    id: '5',
    juridiction: {
      acronym: null,
      acronyms: {
        en: null,
        fr: null,
      },
      createdAt: '2023-10-16T21:48:30-0400',
      dateBegin: null,
      dateEnd: null,
      id: 'G000001',
      lastUpdatedAt: '2023-10-16T21:48:30-0400',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
      organizationId: 1,
      parent: null,
      pseudonymes: {
        en: null,
        fr: null,
      },
      text: 'Université de Montréal',
      typeEtablissement: {
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      uid: 'G000001',
    },
    nom: 'Pavillon J.-A.-DeSève',
    noms: {
      fr: 'Pavillon J.-A.-DeSève',
    },
    pseudonyme: null,
    slug: null,
    text: 'Pavillon J.-A.-DeSève - Campus principal',
    uid: '515A',
  },
  {
    campus: {
      etablissement: {
        id: 'G000001',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        text: 'Université de Montréal',
        uid: 'G000001',
      },
      id: '1',
      nom: 'Campus principal',
      noms: {
        fr: 'Campus principal',
      },
      pseudonyme: null,
      slug: 'campus-principal',
      text: 'Campus principal',
      uid: 'mtl',
    },
    id: '6',
    juridiction: {
      acronym: null,
      acronyms: {
        en: null,
        fr: null,
      },
      createdAt: '2023-10-16T21:48:30-0400',
      dateBegin: null,
      dateEnd: null,
      id: 'G000001',
      lastUpdatedAt: '2023-10-16T21:48:30-0400',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
      organizationId: 1,
      parent: null,
      pseudonymes: {
        en: null,
        fr: null,
      },
      text: 'Université de Montréal',
      typeEtablissement: {
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      uid: 'G000001',
    },
    nom: 'Pav. Direction Des Immeubles',
    noms: {
      fr: 'Pav. Direction Des Immeubles',
    },
    pseudonyme: null,
    slug: null,
    text: 'Pav. Direction Des Immeubles - Campus principal',
    uid: '519A',
  },
  {
    campus: {
      etablissement: {
        id: 'G000001',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        text: 'Université de Montréal',
        uid: 'G000001',
      },
      id: '1',
      nom: 'Campus principal',
      noms: {
        fr: 'Campus principal',
      },
      pseudonyme: null,
      slug: 'campus-principal',
      text: 'Campus principal',
      uid: 'mtl',
    },
    id: '7',
    juridiction: {
      acronym: null,
      acronyms: {
        en: null,
        fr: null,
      },
      createdAt: '2023-10-16T21:48:30-0400',
      dateBegin: null,
      dateEnd: null,
      id: 'G000001',
      lastUpdatedAt: '2023-10-16T21:48:30-0400',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
      organizationId: 1,
      parent: null,
      pseudonymes: {
        en: null,
        fr: null,
      },
      text: 'Université de Montréal',
      typeEtablissement: {
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      uid: 'G000001',
    },
    nom: 'Centre d’éducation physique et des sports (CEPSUM)',
    noms: {
      fr: 'Centre d’éducation physique et des sports (CEPSUM)',
    },
    pseudonyme: null,
    slug: null,
    text: 'Centre d’éducation physique et des sports (CEPSUM) - Campus principal',
    uid: '523A',
  },
  {
    campus: {
      etablissement: {
        id: 'G000001',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        text: 'Université de Montréal',
        uid: 'G000001',
      },
      id: '1',
      nom: 'Campus principal',
      noms: {
        fr: 'Campus principal',
      },
      pseudonyme: null,
      slug: 'campus-principal',
      text: 'Campus principal',
      uid: 'mtl',
    },
    id: '8',
    juridiction: {
      acronym: null,
      acronyms: {
        en: null,
        fr: null,
      },
      createdAt: '2023-10-16T21:48:30-0400',
      dateBegin: null,
      dateEnd: null,
      id: 'G000001',
      lastUpdatedAt: '2023-10-16T21:48:30-0400',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
      organizationId: 1,
      parent: null,
      pseudonymes: {
        en: null,
        fr: null,
      },
      text: 'Université de Montréal',
      typeEtablissement: {
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      uid: 'G000001',
    },
    nom: 'Laboratoire Rene-j-a.-levesque',
    noms: {
      fr: 'Laboratoire Rene-j-a.-levesque',
    },
    pseudonyme: null,
    slug: null,
    text: 'Laboratoire Rene-j-a.-levesque - Campus principal',
    uid: '527A',
  },
  {
    campus: {
      etablissement: {
        id: 'G000001',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        text: 'Université de Montréal',
        uid: 'G000001',
      },
      id: '1',
      nom: 'Campus principal',
      noms: {
        fr: 'Campus principal',
      },
      pseudonyme: null,
      slug: 'campus-principal',
      text: 'Campus principal',
      uid: 'mtl',
    },
    id: '9',
    juridiction: {
      acronym: null,
      acronyms: {
        en: null,
        fr: null,
      },
      createdAt: '2023-10-16T21:48:30-0400',
      dateBegin: null,
      dateEnd: null,
      id: 'G000001',
      lastUpdatedAt: '2023-10-16T21:48:30-0400',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
      organizationId: 1,
      parent: null,
      pseudonymes: {
        en: null,
        fr: null,
      },
      text: 'Université de Montréal',
      typeEtablissement: {
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      uid: 'G000001',
    },
    nom: 'Residence C Etudiants (es)',
    noms: {
      fr: 'Residence C Etudiants (es)',
    },
    pseudonyme: null,
    slug: null,
    text: 'Residence C Etudiants (es) - Campus principal',
    uid: '531A',
  },
  {
    campus: {
      etablissement: {
        id: 'G000001',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        text: 'Université de Montréal',
        uid: 'G000001',
      },
      id: '1',
      nom: 'Campus principal',
      noms: {
        fr: 'Campus principal',
      },
      pseudonyme: null,
      slug: 'campus-principal',
      text: 'Campus principal',
      uid: 'mtl',
    },
    id: '10',
    juridiction: {
      acronym: null,
      acronyms: {
        en: null,
        fr: null,
      },
      createdAt: '2023-10-16T21:48:30-0400',
      dateBegin: null,
      dateEnd: null,
      id: 'G000001',
      lastUpdatedAt: '2023-10-16T21:48:30-0400',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
      organizationId: 1,
      parent: null,
      pseudonymes: {
        en: null,
        fr: null,
      },
      text: 'Université de Montréal',
      typeEtablissement: {
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      uid: 'G000001',
    },
    nom: 'Pavillon Maximilien-Caron',
    noms: {
      fr: 'Pavillon Maximilien-Caron',
    },
    pseudonyme: null,
    slug: null,
    text: 'Pavillon Maximilien-Caron - Campus principal',
    uid: '532A',
  },
];
