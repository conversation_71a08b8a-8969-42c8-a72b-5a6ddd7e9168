import { CivicCampusAddress } from '@/components/civic-campus-address/civic-campus-address';
import { FormSubsection } from '@/components/form-subsection/form-subsection';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CAMPUS_ADDRESS_DEFAULT_VALUE } from '@/constants/common';
import { personFormSections } from '@/constants/directory/person';
import type { PersonFormSchema } from '@/schemas/bottin/person-form-schema';
import { useTranslations } from 'next-intl';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { FiPlus } from 'react-icons/fi';

export const ContactDetails = () => {
  const t = useTranslations(
    'directory.form.sections.description.contactDetails',
  );

  const { control } = useFormContext<PersonFormSchema>();

  const {
    append: appendAddress,
    fields: addressFields,
    remove: removeAddress,
  } = useFieldArray<PersonFormSchema>({
    control,
    name: 'addresses',
  });

  return (
    <Card id={personFormSections.contactDetails.key}>
      <CardHeader>
        <CardTitle className="text-3xl">{t('title')}</CardTitle>
      </CardHeader>
      <CardContent>
        <FormSubsection title={t('fields.address.title')}>
          <div className="flex flex-col gap-y-4">
            <div className="flex flex-col gap-y-3">
              {addressFields.map((field, index) => (
                <CivicCampusAddress
                  key={field.id}
                  addressFieldPath={`addresses.${index}`}
                  totalAddresses={addressFields.length}
                  onRemoveAddress={() => removeAddress(index)}
                  withAddressTypeToggle
                />
              ))}
            </div>
            <Button
              className="self-end"
              onClick={() => appendAddress(CAMPUS_ADDRESS_DEFAULT_VALUE)}
              type="button"
            >
              <FiPlus className="mr-2 h-4 w-4" /> {t('addAddress')}
            </Button>
          </div>
        </FormSubsection>
      </CardContent>
    </Card>
  );
};
